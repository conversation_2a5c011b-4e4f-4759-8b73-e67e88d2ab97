{"miniprogramRoot": "dist/", "projectname": "low-altitude-flight-app", "description": "通航低空行", "appid": "wxcc5ca7686d6ba9f8", "setting": {"urlCheck": true, "es6": false, "enhance": false, "compileHotReLoad": false, "postcss": false, "preloadBackgroundData": false, "minified": false, "newFeature": true, "autoAudits": false, "coverView": true, "showShadowRootInWxmlPanel": false, "scopeDataCheck": false, "useCompilerModule": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false, "minifyWXSS": true}, "compileType": "miniprogram", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "libVersion": "3.7.12", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}