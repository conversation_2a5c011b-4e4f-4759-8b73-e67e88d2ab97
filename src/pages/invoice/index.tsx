import { useState } from 'react';
import { View, Text, Input, Button } from '@tarojs/components';
import CustomHeader from '@/components/CustomHeader';

const Invoice = () => {
  const [invoiceType, setInvoiceType] = useState('enterprise'); // 'enterprise' | 'personal'
  const [formData, setFormData] = useState({
    companyName: '',
    taxId: '',
    email: '',
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <View className='min-h-screen bg-primary-linear'>
      <CustomHeader title='发票开具' bgColor='transparent' showBackButton />

      <View className='py-12 px-16'>
        {/* 电子发票 */}
        <View className='bg-white rounded-12 shadow-sm p-16'>
          <View className='text-16 font-medium text-1D1F20 mb-12'>获取电子发票</View>

          <View className='bg-F84F2A1A rounded-8 p-12 mb-12'>
            <Text className='text-12 text-F84F2A'>
              为了保证您正常收到邮件，建议您使用QQ邮箱。登机凭证仅限已结束的行程申请，未结束行程将无法获取
            </Text>
          </View>

          <View className='mb-16'>
            <View className='text-14 font-medium text-1D1F20 mb-12'>发票类型</View>

            <View className='flex'>
              <View
                className={`flex-1 h-44 flex items-center justify-center rounded-8 mr-12 ${
                  invoiceType === 'enterprise'
                    ? 'bg-white text-primary border-1 border-primary'
                    : 'bg-F7F8FA text-23242D'
                }`}
                onClick={() => setInvoiceType('enterprise')}
              >
                <Text className='text-14'>企业</Text>
              </View>
              <View
                className={`flex-1 h-44 flex items-center justify-center rounded-8 ${
                  invoiceType === 'personal'
                    ? 'bg-white text-primary border-1 border-primary'
                    : 'bg-F7F8FA text-23242D'
                }`}
                onClick={() => setInvoiceType('personal')}
              >
                <Text className='text-14'>个人</Text>
              </View>
            </View>
          </View>

          {/* 表单 */}
          {false && (
            <View>
              <View className='mb-16'>
                <View className='text-14 font-medium text-23242D mb-8'>请输入公司名称</View>

                <Input
                  className='bg-F7F8FA rounded-8 h-40 px-8'
                  placeholder='请输入'
                  placeholderClass='placeholder'
                  value={formData.companyName}
                  onInput={e => handleInputChange('companyName', e.detail.value)}
                />
              </View>

              {/* 纳税人识别号 */}
              <View className='mb-16'>
                <View className='text-14 font-medium text-1D1F20 mb-12'>请输入纳税人识别号</View>
                <Input
                  className='bg-F7F8FA rounded-8 h-40 px-8'
                  placeholder='请输入'
                  placeholderClass='placeholder'
                  value={formData.taxId}
                  onInput={e => handleInputChange('taxId', e.detail.value)}
                />
              </View>

              {/* 电子邮箱 */}
              <View className='mb-16'>
                <View className='text-14 font-medium text-1D1F20 mb-12'>请输入电子邮箱地址</View>
                <Input
                  className='bg-F7F8FA rounded-8 h-40 px-8'
                  placeholder='请输入'
                  placeholderClass='placeholder'
                  value={formData.email}
                  onInput={e => handleInputChange('email', e.detail.value)}
                />
              </View>
            </View>
          )}

          {true && (
            <View>
              {/* 电子邮箱 */}
              <View className='mb-16'>
                <View className='text-14 font-medium text-1D1F20 mb-12'>请输入电子邮箱地址</View>
                <Input
                  className='bg-F7F8FA rounded-8 h-40 px-8'
                  placeholder='请输入'
                  placeholderClass='placeholder'
                  value={formData.email}
                  onInput={e => handleInputChange('email', e.detail.value)}
                />
              </View>
            </View>
          )}
        </View>

        <Button
          className='mt-12 rounded-12 h-48 bg-primary text-16 text-white font-semibold flex items-center justify-center'
          onClick={() => {}}
        >
          申请
        </Button>

        <View className='text-12 text-717C8C mt-12'>
          温馨提示：使用微信扫一扫行程校验单上的二维码，即可对行程单信息做在线校验，扫描结果均经过中国民航官方认证，确保信息有效
        </View>
      </View>
    </View>
  );
};

export default Invoice;
