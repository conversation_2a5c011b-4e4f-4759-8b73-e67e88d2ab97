import { View, Text, <PERSON><PERSON>, Image } from "@tarojs/components";
import cx from "classnames";

interface GraphicDetailsProps {
  className?: string;
}

const GraphicDetails = ({ className }: GraphicDetailsProps) => {
  return (
    <View className={cx("", className)}>
      <View className="bg-white rounded-12 px-16 py-12 shadow-sm">
        <View className="mb-12">
          <Text className="text-14">图文详情</Text>
        </View>

        {[{}, {}, {}].map((item, index) => {
          return (
            <View
              key={index}
              className={cx("w-full h-184 bg-primary rounded-4 overflow-hidden", {
                "mt-8": index !== 0,
              })}
            >
              <Image src="" className="w-full h-full" mode="aspectFit" />
            </View>
          );
        })}
      </View>
    </View>
  );
};

export default GraphicDetails;
