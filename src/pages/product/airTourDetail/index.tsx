import { useRouter } from '@tarojs/taro';
import cx from 'classnames';

import {
  arrowRightSLine,
  arrowRightSLinePrimary,
  map2Fill,
  phoneFill,
  phoneLine,
} from '@/utils/img';

import { View, Text, Button, Image } from '@tarojs/components';
import CustomHeader, { calcHeaderParameter } from '@/components/CustomHeader';
import BottomCtr from '@/components/BottomCtr';
import PriceCalendar from '@/components/PriceCalendar';
import GroupOrder from './GroupOrder';
import PurchaseNotice from './PurchaseNotice';
import GraphicDetails from './GraphicDetails';
import { toUrl } from '@/utils';

const AirTourDetail = () => {
  const router = useRouter();
  const { id } = router.params;

  const { navBarHeight } = calcHeaderParameter();

  return (
    <View className='h-screen'>
      <CustomHeader showBackButton bgColor='transparent' title='详情' />

      <View
        className='w-full h-180 bg-primary'
        style={{
          marginTop: -navBarHeight,
        }}
      >
        图片占位
      </View>

      <View>
        {/* 一 */}
        <View className='bg-F3564B px-16 py-8 flex items-end'>
          <View className='mr-8'>
            <Text className='text-20 text-white font-bold'>¥798</Text>
          </View>
          <View className='mr-12'>
            <Text className='text-10 text-white opacity-75 line-through'>¥1280</Text>
          </View>
          <View>
            <Text className='text-10 text-white opacity-75'>已售180</Text>
          </View>
        </View>
        {/* content */}
        <View className='p-16'>
          {/* 二 */}
          <View className='bg-white px-16 py-8 rounded-12 shadow-sm'>
            {/* Header */}
            <View className='mb-8'>
              <Text className='text-20 font-bold'>南头直升机场空中游览</Text>
            </View>

            {/* Info Sections */}
            <View className='flex'>
              {/* Status Section */}
              <View className='flex-1 border-r-1 border-E6EAF0 flex flex-col'>
                <Text className='text-12 text-primary mb-2'>已关闭</Text>
                <Text className='text-10 text-737578 '>09:00-18:00</Text>
              </View>

              {/* Parking Section */}
              <View className='flex-1 ml-16 px-2 border-r-1 border-E6EAF0 flex flex-col'>
                <Text className='text-12 mb-2'>停车场</Text>
                <Text className='text-10 text-737578 '>免费停车</Text>
              </View>

              {/* Aircraft Section */}
              <View className='flex-1 ml-16 px-2 border-r-1 border-E6EAF0 flex flex-col'>
                <Text className='text-12 mb-2'>机型</Text>
                <Text className='text-10 text-737578 '>三种机型</Text>
              </View>

              {/* All Button */}
              <View className='ml-16 flex items-center justify-center px-4'>
                <View className='flex flex-row items-center'>
                  <Text className='text-12 text-737578 '>全部</Text>
                </View>
                <View className='w-14 h-14 ml-2'>
                  <Image src={arrowRightSLine} className='h-full' mode='aspectFit' />
                </View>
              </View>
            </View>

            {/* Coupon Section */}
            <View className='mt-12 bg-FFD46536 px-8 py-6 rounded-8 flex justify-between items-center'>
              <View className='flex items-center'>
                <Text className='text-12 mr-8'>优惠券</Text>

                {/* Subsidy Tag */}
                <View className='bg-FF7346 rounded-4 px-4 py-2 mr-4 flex items-center'>
                  <Text className='text-white text-10'>补贴50</Text>
                </View>

                {/* Discount Tag */}
                <View className='bg-transparent border-1 border-FF7346 rounded-4 px-4 py-2 flex items-center'>
                  <Text className='text-FF7346 text-10'>满300减100</Text>
                </View>
              </View>

              {/* Get Coupon Button */}
              <Text className='text-primary text-10'>领券</Text>
            </View>
          </View>

          {/* 三 */}
          <View className='rounded-12 mt-12 border-1 border-white py-8 px-16 flex justify-between items-center shadow-sm'>
            <View className='flex-1'>
              <View className='flex items-center'>
                <Text className='text-12 font-medium'>中国四川省成都市双流区XX大道3533号</Text>
                <Image src={arrowRightSLine} className='w-12 h-12' />
              </View>
              <View>
                <Text className='text-10 '>距离18.5km,开车约40分钟</Text>
              </View>
            </View>

            <View className='flex items-center'>
              <Button className='flex flex-col items-center p-0 mr-12'>
                <View className='bg-white rounded-full w-24 h-24 flex justify-center items-center'>
                  <Image src={map2Fill} className='w-14 h-14' />
                </View>
                <Text className='text-10'>地图</Text>
              </Button>
              <Button className='flex flex-col items-center p-0'>
                <View className='bg-white rounded-full w-24 h-24 flex justify-center items-center'>
                  <Image src={phoneFill} className='w-14 h-14' />
                </View>
                <Text className='text-10'>电话</Text>
              </Button>
            </View>
          </View>

          {/* 四 */}
          <View className='mt-12'>
            <View className='text-16 font-medium mb-8'>套餐类型</View>

            {[{}, {}, {}].map((item, index) => {
              return (
                <View
                  key={index}
                  className={cx('bg-white rounded-12 px-16 py-8 shadow-sm', {
                    'mt-8': index !== 0,
                  })}
                >
                  <View className='mb-8'>
                    <Text className='text-14 font-medium'>千岛湖3300米跳伞+教练含跳伞装备</Text>
                  </View>

                  <View className='mb-12'>
                    <View>
                      <Text className='text-12 text-737578'>
                        门票: 杭州市浙江杭州市建德千岛湖高空跳伞1张
                      </Text>
                    </View>

                    <View>
                      <Text className='text-12 text-737578'>特色活动: 跳伞1次</Text>
                    </View>
                  </View>

                  <View className='flex items-center justify-between'>
                    <View className='flex items-center'>
                      <Text className='text-FF7346 text-20 font-medium mr-8'>¥1980</Text>

                      <View className='flex items-center'>
                        <Text className='text-primary text-12'>套餐说明</Text>
                        <Image src={arrowRightSLinePrimary} className='w-14 h-14' />
                      </View>
                    </View>

                    <View>
                      <Button
                        className='bg-F84F2A1A text-FF7346 text-12 rounded-4 px-12 py-4 leading-normal'
                        onClick={() => {
                          toUrl('/pages/product/airTourOrderDetail/index');
                        }}
                      >
                        立即购买
                      </Button>
                    </View>
                  </View>
                </View>
              );
            })}
          </View>

          {/* 五 拼单列表 */}
          <GroupOrder className='mt-12' />

          {/* 六 购买须知 */}
          <PurchaseNotice className='mt-12' />

          {/* 七 图文详情 */}
          <GraphicDetails className='mt-12' />

          <PriceCalendar
            customActionSheetProps={{
              title: '请选择预定时间',
              visible: true,
              onConfirm: val => {
                console.log('🚀🚀🚀 ~ val:', val);
              },
            }}
            calendarCardProps={{
              startDate: new Date(),
            }}
          />
        </View>

        <BottomCtr className='flex justify-between'>
          <Button className='bg-transparent h-40 flex flex-col items-center justify-center'>
            <Image src={phoneLine} className='w-14 h-14' />
            <Text className='text-10'>联系我们</Text>
          </Button>

          <Button
            className='rounded-12 w-264 h-40 py-8 bg-F3564B text-white text-16 flex justify-center items-center'
            onClick={() => {
              toUrl('/pages/product/airTourOrderDetail/index');
            }}
          >
            立即购买
          </Button>
        </BottomCtr>
      </View>
    </View>
  );
};

export default AirTourDetail;
