import { View, Text, Image, Button } from "@tarojs/components";
import cx from "classnames";

import { arrowRightSLine } from "@/utils/img";
import { useState } from "react";
import CustomActionSheet from "@/components/CustomActionSheet";

interface PurchaseNoticeProps {
  className?: string;
}

const PurchaseNotice = ({ className }: PurchaseNoticeProps) => {
  const [visible, setVisible] = useState(false);

  const noticeItems = [
    {
      title: "退款保障",
      content: "未预约随时退·过期自动退",
    },
    {
      title: "有效期限",
      content: "购买后60天内有效",
    },
    {
      title: "预约须知",
      content: "购买后可预约当天",
    },
    {
      title: "使用限制",
      content: "限1人使用",
    },
    {
      title: "适用人群",
      content: "特殊疾病乘客、4岁以下儿童不能乘机、4岁以下下下下下下下",
    },
  ];

  const detailItems = [
    {
      title: "退款保障",
      contents: ["未预约随时退·过期自动退"],
    },
    {
      title: "有效期限",
      contents: ["购买后60天内有效"],
    },
    {
      title: "预约须知",
      contents: ["购买后可预约当天"],
    },
    {
      title: "使用限制",
      contents: ["限1人使用"],
    },
    {
      title: "适用人群",
      contents: [
        "特殊疾病乘客、4岁以下儿童不能乘机;",
        "平台温馨提醒： 您在到店使用本商品/服务期间，如涌及潜水、骑马、滑雪、热气球、游艇等项目，请关注商家的安全提示内容，了解相关注意事项，做好安全防护措施，保护您的安全。",
      ],
    },
  ];

  return (
    <View className={cx("", className)}>
      <View className="bg-white rounded-12 px-16 py-12 shadow-sm">
        <View className="flex justify-between items-center mb-8">
          <Text className="text-14">购买须知</Text>
          <View>
            <Button
              className="flex items-center bg-transparent p-0"
              onClick={() => setVisible(true)}
            >
              <Image src={arrowRightSLine} className="w-14 h-14" />
            </Button>
          </View>
        </View>

        {noticeItems.map((item, index) => (
          <View key={item.title} className={cx("flex py-2", {})}>
            <View className="mr-8">
              <Text className="text-12">{item.title}</Text>
            </View>
            <View className="w-100 flex-1 truncate">
              <Text className="text-12 text-737578">{item.content}</Text>
            </View>
          </View>
        ))}
      </View>

      <CustomActionSheet
        visible={visible}
        title="购买须知"
        onCancel={() => setVisible(false)}
      >
        <View className="max-h-360 overflow-y-auto py-12 text-left">
          {detailItems.map((item, index) => (
            <View key={item.title} className={cx({ "mt-8": index !== 0 })}>
              <View className="mb-8">
                <Text className="text-14 font-medium">{item.title}</Text>
              </View>

              {item.contents.map((content, contentIndex) => (
                <View
                  key={`${item.title}-${contentIndex}`}
                  className={cx("flex items-center", {
                    "mb-8": contentIndex !== item.contents.length - 1,
                  })}
                >
                  <View className="w-4 h-4 bg-737578 rounded-full mr-8 mt-1 flex-shrink-0"></View>
                  <Text className="text-14 text-737578">{content}</Text>
                </View>
              ))}
            </View>
          ))}
        </View>
      </CustomActionSheet>
    </View>
  );
};

export default PurchaseNotice;
