@import '../../../../../styles/variables.less';

.order-steps {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;

  .step-item-container {
    flex: 1;
    //display: flex;
    //align-items: flex-start;
    //justify-content: center;
    //gap: 4px;
    &:last-child {
      flex: 0;
    }
    .step-icon {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 8px;
      margin-bottom: 4px;

      .icon-image {
        width: 32px;
        height: 32px;
      }
      .step-line {
        width: calc(100% - 36px);
        height: 2px;
        border-top: 2px dashed #4c6787;
      }
      .active {
        border-top: 2px solid #1d9938;
      }
    }

    .step-title {
      font-size: 12px;
      color: #828998;
      text-align: center;
      white-space: nowrap;

      &.active {
        color: #1d9938;
        font-weight: 500;
      }
    }
  }
}
