@import '../../../../../styles/variables.less';

.order-steps-demo {
  padding: 16px;
  background-color: #f0f4fa;
  min-height: 100vh;

  .demo-header {
    margin-bottom: 20px;
    
    .demo-title {
      font-size: 18px;
      font-weight: 500;
      color: @color-text-primary;
    }
  }

  .demo-content {
    .card-box {
      background: #fff;
      border-radius: 12px;
      padding: 20px 16px;
      margin-bottom: 20px;
    }

    .step-controls {
      background: #fff;
      border-radius: 12px;
      padding: 20px 16px;

      .control-title {
        font-size: 16px;
        color: @color-text-primary;
        margin-bottom: 16px;
        display: block;
      }

      .button-group {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .step-button {
          flex: 1;
          min-width: 80px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          background-color: #f2f6fc;
          color: #4f5170;
          border-radius: 8px;
          font-size: 14px;
          
          &.active {
            background-color: @color-primary;
            color: #fff;
          }
        }
      }
    }
  }
}
