import { memo } from 'react';
import { View, Image, Text } from '@tarojs/components';
import './index.less';
import {
  done1,
  done2,
  done3,
  done4,
  process1,
  process2,
  process3,
  process4,
  undone1,
  undone2,
  undone3,
  undone4,
} from '@/utils/img';

interface OrderStepsProps {
  currentStep: number; //当前步骤
  className?: string;
}

const OrderSteps = ({ currentStep = 1, className = '' }: OrderStepsProps) => {
  // 步骤配置
  const steps = [
    { title: '交付费用', icon1: done1, icon2: process1, icon3: undone1 },
    { title: '定制方案', icon1: done2, icon2: process2, icon3: undone2 },
    { title: '承运', icon1: done3, icon2: process3, icon3: undone3 },
    { title: '订单完成', icon1: done4, icon2: process4, icon3: undone4 },
    // { title: '拼团中', icon1: done5, icon3: undone4 },
  ];

  // 获取步骤状态图标
  const getStepIcon = (index: number) => {
    const step = steps[index];
    if (index + 1 < currentStep) {
      // 已完成
      return step.icon1;
    } else if (index + 1 === currentStep) {
      // 进行中
      return step.icon2 || step.icon3; // 如果没有进行中图标，使用未完成图标
    } else {
      // 未开始
      return step.icon3;
    }
  };

  return (
    <View className={`order-steps ${className}`}>
      {steps.map((step, index) => (
        <View key={index} className='step-item-container'>
          <View className='step-icon'>
            <Image src={getStepIcon(index)} className='icon-image' />
            {index < steps.length - 1 && (
              <View className={`step-line ${index + 2 <= currentStep ? 'active' : ''}`} />
            )}
          </View>
          <Text className={`step-title ${index + 2 <= currentStep ? 'active' : ''}`}>
            {step.title}
          </Text>
        </View>
      ))}
    </View>
  );
};

export default memo(OrderSteps);
