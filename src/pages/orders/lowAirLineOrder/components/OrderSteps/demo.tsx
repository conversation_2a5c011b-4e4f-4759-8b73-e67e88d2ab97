import { memo, useState } from 'react';
import { View, Button, Text } from '@tarojs/components';
import OrderSteps from './index';
import './demo.less';

const OrderStepsDemo = () => {
  const [currentStep, setCurrentStep] = useState(1);

  const handleStepChange = (step: number) => {
    setCurrentStep(step);
  };

  return (
    <View className="order-steps-demo">
      <View className="demo-header">
        <Text className="demo-title">订单步骤组件演示</Text>
      </View>

      <View className="demo-content">
        <View className="card-box">
          <OrderSteps currentStep={currentStep} />
        </View>

        <View className="step-controls">
          <Text className="control-title">当前步骤: {currentStep}</Text>
          <View className="button-group">
            {[1, 2, 3, 4, 5].map((step) => (
              <Button
                key={step}
                className={`step-button ${currentStep === step ? 'active' : ''}`}
                onClick={() => handleStepChange(step)}
              >
                步骤 {step}
              </Button>
            ))}
          </View>
        </View>
      </View>
    </View>
  );
};

export default memo(OrderStepsDemo);
