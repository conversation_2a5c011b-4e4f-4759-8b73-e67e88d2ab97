.low-airline-filter {
  padding: 12px 16px;
  box-sizing: border-box;
  .filter-item {
    margin-bottom: 12px;
    .title {
      font-size: 16px;
      color: #23242d;
      font-weight: bold;
      margin-bottom: 12px;
    }
    .filter-item-content {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 12px;
      gap: 8px;
      .time-box {
        flex: 1;
        display: inline-flex;
        align-items: center;
        justify-content: space-between;
        background: #f2f6fc;
        border-radius: 12px;
        padding: 10px 16px;
        box-sizing: border-box;
        color: #9aa2ca;
        image {
          width: 12px;
          height: 12px;
        }
        &.active {
          color: #23242d;
        }
      }
    }
    .time-select-box {
      align-items: center;
      justify-content: space-between;
    }
  }
  .filter-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    box-sizing: border-box;
    padding: 20px 0 0 0;
    .filter-btn {
      flex: 1;
      border-radius: 12px;
      font-weight: bold;
    }
    .cancel-btn {
      background: #fff;
      border: 1px solid #1663f8;
      color: #1663f8;
    }
    .sure-btn {
      background: #1663f8;
      border: 1px solid transparent;
      color: #fff;
    }
  }
}
