import { memo, useState } from 'react';
import { Button, Image, View } from '@tarojs/components';
import './index.less';
import { ORDER_STATUS } from '@/pages/orders/constant';
import CustomTag from '@/components/CustomTag';
import { calendarIcon } from '@/utils/img';
import CustomCalendar from '@/components/CustomCalendar';

const LowAirLineFilter = () => {
  const [filterForm, setFilterForm] = useState<any>({ orderType: '', orderTime: [] });
  const [datePickerVisible, setDatePickerVisible] = useState('');
  return (
    <>
      <View className={'low-airline-filter'}>
        <View className={'filter-item'}>
          <View className={'title'}>订单类型:</View>
          <View className={'filter-item-content'}>
            {ORDER_STATUS?.map(item => {
              return (
                <CustomTag
                  size={'large'}
                  key={item.value}
                  type={filterForm?.orderType === item.value ? 'primary' : 'ghost'}
                  onClick={() => setFilterForm({ ...filterForm, orderType: item.value })}
                >
                  {item.label}
                </CustomTag>
              );
            })}
          </View>
        </View>
        <View className={'filter-item'}>
          <View className={'title'}>预定时间:</View>
          <View className={'filter-item-content time-select-box'}>
            <View
              className={`start-time time-box ${filterForm.orderTime[0] ? 'active' : ''}`}
              onClick={() => setDatePickerVisible('start')}
            >
              {filterForm.orderTime[0] ? filterForm.orderTime[0] : '请选择'}
              <Image src={calendarIcon} />
            </View>
            <View className={'interval-time'}>-</View>
            <View
              className={`end-time  time-box ${filterForm.orderTime[0] ? 'active' : ''}`}
              onClick={() => setDatePickerVisible('end')}
            >
              {filterForm.orderTime[0] ? filterForm.orderTime[0] : '请选择'}
              <Image src={calendarIcon} />
            </View>
          </View>
        </View>
        <View className={'filter-footer'}>
          <Button
            className={'filter-btn  cancel-btn'}
            onClick={() => setFilterForm({ orderType: '', orderTime: [] })}
          >
            重置
          </Button>
          <Button className={'filter-btn sure-btn'} type={'primary'}>
            确定
          </Button>
        </View>
        <CustomCalendar
          customActionSheetProps={{
            title: '请选择预定时间',
            visible: !!datePickerVisible,
            onClose: () => setDatePickerVisible(''),
            onConfirm: () => {
              // if (datePickerVisible === 'start') {
              //   const obj = { ...filterForm };
              //   obj.orderTime[0] = value;
              //   setFilterForm(obj);
              // } else if (datePickerVisible === 'end') {
              //   const obj = { ...filterForm };
              //   obj.orderTime[1] = value;
              //   setFilterForm(obj);
              // }
              setDatePickerVisible('');
            },
          }}
        />
      </View>
    </>
  );
};
export default memo(LowAirLineFilter);
