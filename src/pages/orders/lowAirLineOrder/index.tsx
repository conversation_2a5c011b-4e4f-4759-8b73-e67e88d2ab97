import { memo } from 'react';
import './index.less';
import { Image, View, Text, Button } from '@tarojs/components';
import { lowAirlineIcon } from '@/utils/img';
import { judgeStatusTag } from '@/pages/orders/constant';
import AirLineCard from '@/pages/lowAltitudeAirline/compoents/AirLineCard';

const LowAirLineOrder = () => {
  return (
    <>
      <View className={'low-airline-order card-box'}>
        <View className={'order-item-header'}>
          <View className={'order-item-header-left'}>
            <Image src={lowAirlineIcon} />
            <Text className={''}>包机</Text>
          </View>
          <View className={'order-item-header-right'}>{judgeStatusTag('1')}</View>
        </View>
        <View className={'order-item-body'}>
          <AirLineCard type={'order'} />
        </View>
        <View className={'order-item-footer'}>
          <View className={'split-dash-line'} />
          <View className={'order-item-footer-body'}>
            <View>
              <Button className={'cancel-btn'}>取消</Button>
            </View>
            <View>
              <Button className={'sure-btn'}>付款</Button>
            </View>
          </View>
        </View>
      </View>
    </>
  );
};
export default memo(LowAirLineOrder);
