import { useState, memo } from 'react';
import { View, Text, <PERSON><PERSON>, Picker } from '@tarojs/components';
import './index.less';
import CustomHeader from '@/components/CustomHeader';
import AirLineCard from '@/pages/lowAltitudeAirline/compoents/AirLineCard';
import { lowAirLineOrderDetailBg } from '@/utils/img';
import BottomCtr from '@/components/BottomCtr';
import { ArrowRightSmall } from '@nutui/icons-react-taro';
import CustomActionSheet from '@/components/CustomActionSheet';
import cx from 'classnames';
import { DatePicker } from '@nutui/nutui-react-taro';
import CustomTag from '@/components/CustomTag';

const AERIAL_TOUR_REASONS = [
  { id: 1, text: '出行计划有变', primary: true },
  { id: 2, text: '订错/多订' },
  { id: 3, text: '其他渠道更优惠' },
  { id: 4, text: '身体不适' },
  { id: 5, text: '无法入住' },
  { id: 6, text: '酒店加价' },
  { id: 7, text: '对酒店不满意' },
  { id: 8, text: '交通停运/延误' },
  { id: 9, text: '自然灾害' },
];

const ChangeDate = () => {
  const [date, setDate] = useState('2025年4月29日');
  const [reason, setReason] = useState('我的计划有变，自愿改变行程计划');
  const [reasonModal, setReasonModal] = useState(false);
  const [dateModal, setDateModal] = useState(false);
  // const handleSelectDate = value => {
  //   setDate(value);
  // };
  //
  // const handleSelectReason = value => {
  //   setReason(value);
  // };

  return (
    <View
      className='change-date'
      style={{ background: `url(${lowAirLineOrderDetailBg}) no-repeat center center/cover` }}
    >
      {/* 头部区域 */}
      <CustomHeader title={'改期'} bgColor={'transparent'} showBackButton={true} />

      <View className={'content-container'}>
        {/* 改期产品信息 */}
        <View className='card-box product-info '>
          <View className='title'>改期产品</View>
          <View className={'border-box'}>
            <AirLineCard type={'orderDetail'} />
          </View>

          {/* 改期规则提示 */}
          <View className='card-box rule-tip'>
            <Text>
              低空行不加收任何退改服务费，具体按航空公司规定收取， 点击可查看
              <Text className='rule-link'>
                退改签规则 <ArrowRightSmall color={'#4f5170'} width={12} height={12} />
              </Text>
            </Text>
          </View>
        </View>

        {/* 改期日期选择 */}
        <View className='card-box form-item'>
          <View className='title'>请选择改期日期</View>
          <View className='picker' onClick={() => setDateModal(true)}>
            <Text className='value'>{date}</Text>
            <ArrowRightSmall color={'#4f5170'} width={12} height={12} />
          </View>
        </View>

        {/* 改期原因选择 */}
        <View className='card-box form-item'>
          <View className='title'>请选择改期原因</View>
          <View className='picker' onClick={() => setReasonModal(true)}>
            <Text className='value'>{reason}</Text>
            <ArrowRightSmall color={'#4f5170'} width={12} height={12} />
          </View>
        </View>

        {/* 温馨提示 */}
        <View className='card-box warning'>
          <View className='title'>温馨提示</View>
          <View className='content'>
            如果您的订单中包含多人，请分开提交改期申请。暂不支持儿童改期，如果您的订单中包含儿童乘客，请您拨打出票方客服电话023-63560494办理。
          </View>
        </View>
      </View>

      {/* 底部按钮 */}
      <BottomCtr>
        <View className='bottom-actions'>
          <Button className='action-btn'>下一步</Button>
        </View>
      </BottomCtr>
      {/* 改期原因选择弹窗 */}
      <CustomActionSheet
        visible={reasonModal}
        title='请选择改期原因'
        onClose={() => setReasonModal(false)}
      >
        <View className='pt-16 flex flex-wrap gap-12'>
          {AERIAL_TOUR_REASONS.map(item => (
            <CustomTag type={item.primary ? 'blue' : 'default'} key={item.id} size={'large'}>
              {item.text}
            </CustomTag>
          ))}

          <View className='flex items-center w-full pt-12'>
            <View className='flex-1 mr-10'>
              <Button className='rounded-12 h-48 bg-white text-16 text-124072 font-semibold border-1 border-124072 flex items-center justify-center'>
                取消
              </Button>
            </View>
            <View className='flex-1'>
              <Button
                className='rounded-12 h-48 bg-124072 text-16 text-white font-semibold flex items-center justify-center'
                onClick={() => {
                  // navigateTo({
                  //   url: '/pages/orders/travelOrders/refundDetails/index',
                  // });
                }}
              >
                确认提交
              </Button>
            </View>
          </View>
        </View>
      </CustomActionSheet>
      <DatePicker
        title='日期选择'
        visible={dateModal}
        // pickerProps={{
        //   popupProps: { zIndex: 1220 },
        // }}
        defaultValue={new Date(date)}
        showChinese
        onClose={() => setReasonModal(false)}
        onConfirm={(_selectedOptions, selectedValue) => {
          console.log(selectedValue);
        }}
      />
    </View>
  );
};

export default memo(ChangeDate);
