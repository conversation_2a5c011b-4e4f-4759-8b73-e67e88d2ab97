.low-airline-order-detail {
  color: #fff;
  font-size: 14px;
  //padding: 16px;
  width: 100%;
  height: 100vh;
  .content-container {
    padding: 16px;
  }
  .customizing-status {
    margin-bottom: 8px;
    display: flex;
    align-items: center;

    .customizing-icon {
      width: 28px;
      height: 28px;
      margin-right: 8px;
    }

    .customizing-text {
      font-size: 20px;
      font-weight: bold;
      color: #f5ca76;
    }
  }

  .notice {
    margin-bottom: 16px;
    color: #f2f6fc;
    font-size: 12px;
  }

  .content {
    margin-bottom: 16px;
    .trip-info,
    .contact-info {
      margin-top: 16px;

      .title {
        font-size: 14px;
        font-weight: 500;

        color: #4f5170;
        margin-bottom: 10px;
      }

      .info {
        background-color: #f0f4fa;

        .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .label {
            font-size: 14px;
            color: #23242d;
          }

          .value {
            font-size: 14px;
            color: #4f5170;
          }
        }
      }

      .collapse {
        margin-top: 8px;
        text-align: center;
        color: #2196f3;
        cursor: pointer;

        .arrow-icon {
          font-size: 18px;
        }
      }

      /* 收起状态 */
      &.trip-info .info.collapsed {
        height: 0;
        overflow: hidden;
      }
    }
  }

  // 订单步骤样式已移至 OrderSteps 组件

  .bottom-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;

    .action-btn {
      flex: 1;
      height: 48px;
      line-height: 48px;
      text-align: center;
      border-radius: 8px;
      margin: 0 8px;
      font-size: 16px;

      &.cancel {
        background-color: rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.6);
      }

      &.change {
        background-color: #fff;
        color: #124072;
      }
    }
  }
}
