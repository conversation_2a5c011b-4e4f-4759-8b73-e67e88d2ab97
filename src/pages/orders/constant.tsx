import CustomTag from '@/components/CustomTag';

export const ORDER_STATUS = [
  { label: '待支付', value: '1' },
  { label: '拼团中', value: '2' },
  { label: '定制中', value: '3' },
  { label: '拼团成功', value: '4' },
  { label: '包机成功', value: '5' },
  { label: '已结束', value: '6' },
];

export const judgeStatusTag = (status: string) => {
  switch (status) {
    case '1':
      return <CustomTag type={'warning'}>待支付</CustomTag>;
    case '2':
      return <CustomTag type={'info'}>拼团中</CustomTag>;
    case '3':
      return <CustomTag type={'normal'}>定制中</CustomTag>;
    case '4':
      return <CustomTag type={'success'}>拼团成功</CustomTag>;
    case '5':
      return <CustomTag type={'success'}>包机成功</CustomTag>;
    case '6':
      return <CustomTag type={'default'}>已结束</CustomTag>;
    default:
      return '';
  }
};
