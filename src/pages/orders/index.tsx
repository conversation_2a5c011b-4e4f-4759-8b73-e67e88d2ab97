import { memo, useState } from 'react';
import cx from 'classnames';

import { Button, View } from '@tarojs/components';
import CustomHeader from '@/components/CustomHeader';
import CustomPopup from '@/components/CustomPopup';
import TravelOrders from './travelOrders';
import TravelOrderFilter from './travelOrders/TravelOrderFilter';
import LowAirLineOrder from '@/pages/orders/lowAirLineOrder';
import LowAirLineFilter from '@/pages/orders/lowAirLineOrder/components/lowAirlineFilter';

const Orders = () => {
  const filterList = [
    {
      label: '机票订单',
      value: '1',
      com: <View></View>,
      filterCom: <View></View>,
    },
    {
      label: '旅游订单',
      value: '2',
      com: <TravelOrders />,
      filterCom: <TravelOrderFilter />,
    },
    {
      label: '包机订单',
      value: '3',
      com: <LowAirLineOrder />,
      filterCom: <LowAirLineFilter />,
    },
  ];

  const [cur, setCur] = useState(filterList[2]);
  const [filterInfo, setFilterInfo] = useState({
    visible: false,
    filterCom: filterList[2].filterCom,
  }); // 筛选信息

  return (
    <View className='min-h-screen bg-primary-linear'>
      <CustomHeader title='我的订单' bgColor={'transparent'} />

      <View className='px-16 py-12'>
        {/* Top */}
        <View className='flex items-center justify-between'>
          <View className='bg-F4F8FD flex items-center rounded-4 p-4'>
            {filterList.map((item, index) => {
              return (
                <Button
                  key={index}
                  className={cx('rounded-4 py-2 mx-2 text-14 leading-normal', {
                    'text-primary font-medium bg-white shadow-sm': cur.value === item.value,
                    'text-737578 bg-transparent': cur.value !== item.value,
                  })}
                  onClick={() => setCur(item)}
                >
                  {item.label}
                </Button>
              );
            })}
          </View>

          <View
            className={cx('bg-F4F8FD flex items-center rounded-4 p-4', {
              'relative z-max': filterInfo.visible,
            })}
          >
            <Button
              className='rounded-4 py-2 mx-2 text-14 leading-normal text-737578'
              onClick={() => {
                setFilterInfo({
                  visible: true,
                  filterCom: cur.filterCom,
                });
              }}
            >
              筛选
            </Button>
          </View>
        </View>

        {/* Container */}
        <View className='mt-12'>{cur.com}</View>

        {/* Filter */}
        <CustomPopup
          title='筛选'
          visible={filterInfo.visible}
          onClose={() => {
            setFilterInfo(old => ({
              ...old,
              visible: false,
            }));
          }}
        >
          {filterInfo.filterCom}
        </CustomPopup>
      </View>
    </View>
  );
};
export default memo(Orders);
