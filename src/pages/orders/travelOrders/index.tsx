import { useLoad, navigateTo } from '@tarojs/taro';
import cx from 'classnames';
import { View, Text, Image, Button } from '@tarojs/components';

import { parachute, aerialTour } from '@/utils/img';

const TravelOrders = () => {
  useLoad(() => {
    console.log('TravelOrders loaded.');
  });

  return (
    <View>
      {/* 订单数据 */}
      {[
        {
          title: '空中跳伞',
          image: parachute,
          status: '待支付',
          packageName: '蓝色气流成都跳伞',
          description: '跳伞+手持摄像+第三方摄像',
          playTime: '2022-08-29',
          price: '1070',
        },
        {
          title: '空中游览',
          image: aerialTour,
          status: '待使用',
          packageName: '【真机驾驶】进阶体验深度飞行套餐',
          description: '洛带古镇空中观光10-20分钟，颁发飞行证书',
          playTime: '2022-08-29',
          price: '1070',
        },
      ].map((item, index) => (
        <View
          key={index}
          className={cx('bg-white shadow-sm rounded-8 p-16', {
            'mt-12': index !== 0,
          })}
          onClick={() => {
            navigateTo({
              url: `pages/orders/travelOrders/orderDetails/index?id=111111111111`,
            });
          }}
        >
          <View className='flex items-center justify-between mb-12'>
            <View className='flex items-center'>
              <Image className='w-20 h-20 rounded-lg mr-8' src={item.image} />
              <Text className='text-14 font-medium'>{item.title}</Text>
            </View>

            <View
              className={cx('px-4 py-2 rounded-4 text-12 font-semibold', {
                'bg-FAF2F0 text-F84F2A ': item.status === '待支付',
                'bg-00000014 text-4F5170 ': item.status === '待使用',
              })}
            >
              {item.status}
            </View>
          </View>

          <View className='mb-8'>
            <Text className='text-16 text-23242D font-semibold'>{item.packageName}</Text>
          </View>

          <View className='mb-8'>
            <Text className='text-14 text-4F5170 font-medium'>{item.description}</Text>
          </View>

          <View className='mb-8'>
            <Text className='text-12 text-9AA2CA'>游玩时间：{item.playTime}</Text>
          </View>

          <View className='flex justify-end pt-4'>
            <Text className='text-12 text-4F5170 font-medium'>实际支付：¥{item.price}</Text>
          </View>

          {item.status === '待支付' && (
            <View className='border-t-1 border-E6EAF0 border-dashed pt-12 mt-8 flex justify-end'>
              <View className='mr-8'>
                <Button className='rounded-8 h-32 px-20 flex items-center justify-center text-14 font-medium bg-white border-1 border-9AA2CA text-4F5170'>
                  取消
                </Button>
              </View>
              <View className=''>
                <Button className='rounded-8 h-32 px-20 flex items-center justify-center text-14 font-medium bg-white border-1 border-primary text-primary'>
                  付款
                </Button>
              </View>
            </View>
          )}
        </View>
      ))}
    </View>
  );
};

export default TravelOrders;
