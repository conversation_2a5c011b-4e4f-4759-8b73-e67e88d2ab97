import { useLoad, navigateTo } from '@tarojs/taro';
import cx from 'classnames';

import CustomHeader from '@/components/CustomHeader';
import { View, Text, Image, Button } from '@tarojs/components';
import OnlineService from '@/components/OnlineService';

import {
  arrowRightSLine,
  arrowRightSLine1663F8,
  checkboxCircleFillFail,
  checkboxCircleFillLoading,
  checkboxCircleFillSuccess,
} from '@/utils/img';
import BottomCtr from '@/components/BottomCtr';

const Page = () => {
  useLoad(() => {
    console.log('Page loaded.');
  });

  return (
    <View className='min-h-screen bg-primary-linear'>
      <CustomHeader showBackButton title='订单详情' bgColor='transparent' />

      <View className='py-12 px-16'>
        {/* 退款 */}
        <View className='bg-white rounded-12 p-16 shadow-sm mb-12'>
          <View className='flex items-center justify-between mb-12'>
            <Text className='text-20 font-semibold'>已退款</Text>
            <View className='flex items-center'>
              <View className='flex items-end mr-4'>
                <Text className='text-14 text-primary font-medium'>¥</Text>
                <Text className='text-20 text-primary font-medium'>1070</Text>
              </View>
              <Image src={arrowRightSLine1663F8} className='w-18 h-18' />
            </View>
          </View>

          <View className='text-12 text-6F7C8F'>
            退款已完成，您的票款将退回至原支付账户，第三方支付平台到账时间不同，请留意原账户明细。如有疑问请及时联系客服电话023-63560494
          </View>
        </View>

        {/* 退款进度 */}
        <View className='bg-white rounded-12 p-16 shadow-sm mb-12'>
          <View className='text-16 font-semibold mb-12'>退款进度</View>

          {/* 步骤点 */}
          <View className='w-full flex items-center'>
            <View className='flex flex-col items-center'>
              <Image src={checkboxCircleFillSuccess} className='w-18 h-18 mb-4' />
              <Text className='text-14 text-4F5170'>退款申请已提交</Text>
            </View>
            <View className={cx('flex-1 border-t-2 border-E6EAF0 mx-6', {})}></View>
            <View className='flex flex-col items-center'>
              <Image src={checkboxCircleFillLoading} className='w-18 h-18 mb-4' />
              <Text className='text-14 text-4F5170'>退款申请中...</Text>
            </View>
            <View className={cx('flex-1 border-t-2 border-E6EAF0 mx-6', {})}></View>
            <View className='flex flex-col items-center'>
              <Image src={checkboxCircleFillFail} className='w-18 h-18 mb-4' />
              <Text className='text-14 text-4F5170'>退款失败</Text>
            </View>
          </View>
        </View>

        {/* 产品详情卡片 */}
        <View className='bg-white rounded-12 p-16 shadow-sm mb-12'>
          <View className='mb-8 flex items-center justify-between'>
            <Text className='text-20 font-semibold text-1D1F20'>产品详情</Text>

            <View className=' bg-FAF2F0 py-2 px-4 rounded-4'>
              <Text className='text-12 text-F84F2A'>有退改</Text>
            </View>
          </View>

          <View className='mb-8'>
            <Text className='text-16 text-23242D font-semibold'>蓝色气流成都跳伞</Text>
          </View>

          <View className='mb-8'>
            <Text className='text-14 text-4F5170 font-medium'>跳伞+手持摄像+第三方摄像</Text>
          </View>

          <View className=''>
            <Text className='text-12 text-9AA2CA'>游玩日期: 2022-08-29</Text>
          </View>
        </View>

        {/* 使用规则 */}
        <View className='bg-white rounded-12 p-16 shadow-sm'>
          <View className='text-16 font-bold text-1D1F20 mb-12'>使用规则</View>

          {/* 规则列表 */}
          {[
            { title: '有条件退改', hasArrow: true },
            { title: '已使用不支持退改', hasArrow: true },
          ].map((item, index) => (
            <View
              key={index}
              className={cx('flex items-center justify-between', {
                'mt-8': index !== 0,
              })}
            >
              <Text className='text-14 text-4F5170'>{item.title}</Text>
              {item.hasArrow && <Image src={arrowRightSLine} className='w-14 h-14' />}
            </View>
          ))}
        </View>

        {/* 出行人信息 */}
        <View className='mt-12 p-16 bg-white rounded-12 shadow-sm'>
          <View className='mb-12 flex items-center justify-between'>
            <Text className='text-16 font-bold text-1D1F20'>出行人信息</Text>
            <View
              className='flex items-center'
              onClick={() => {
                // 处理查看隐藏信息的点击事件
                console.log('查看隐藏信息');
              }}
            >
              <Text className='text-12 text-4F5170 mr-4'>查看隐藏信息</Text>
              <Image src={arrowRightSLine} className='w-14 h-14' />
            </View>
          </View>

          <View className='bg-F2F6FC py-12 px-16 rounded-12'>
            <View className='flex items-center mb-12'>
              <Text className='text-16 text-1D1F20 mr-8'>安妮</Text>
              <Text className='text-14 text-4F5170'>成人</Text>
            </View>

            <View className='mb-12'>
              <Text className='text-12 text-666666'>身份证号：</Text>
              <Text className='text-12 text-666666'>5138**********029</Text>
            </View>

            <View>
              <Text className='text-12 text-666666'>联系电话：</Text>
              <Text className='text-12 text-666666'>173-1231-3102</Text>
            </View>
          </View>
        </View>

        {/* 在线客服 */}
        <OnlineService className='mt-12' />

        <BottomCtr className='flex items-center'>
          <View className='flex-1 px-6'>
            <Button
              className='h-36 bg-white rounded-4 border-1 border-00000014 text-primary text-14'
              onClick={() => {}}
            >
              退款
            </Button>
          </View>
          <View className='flex-1 px-6'>
            <Button
              className='h-36 bg-white rounded-4 border-1 border-00000014 text-primary text-14'
              onClick={() => {}}
            >
              电子发票
            </Button>
          </View>
        </BottomCtr>
      </View>
    </View>
  );
};

export default Page;
