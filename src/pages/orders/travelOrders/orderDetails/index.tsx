import { useLoad, navigateTo } from '@tarojs/taro';
import cx from 'classnames';
import { useState } from 'react';

import { View, Text, Button, Image } from '@tarojs/components';
import CustomHeader from '@/components/CustomHeader';
import OnlineService from '@/components/OnlineService';
import BottomCtr from '@/components/BottomCtr';

import { arrowRightSLine } from '@/utils/img';
import CustomActionSheet from '@/components/CustomActionSheet';

const REFUND_REASONS = [
  { id: 1, text: '出行计划有变', primary: true },
  { id: 2, text: '订错/多订' },
  { id: 3, text: '其他渠道更优惠' },
  { id: 4, text: '身体不适' },
  { id: 5, text: '无法入住' },
  { id: 6, text: '酒店加价' },
  { id: 7, text: '对酒店不满意' },
  { id: 8, text: '交通停运/延误' },
  { id: 9, text: '自然灾害' },
];

const OrderDetails = () => {
  const [isRefundVisible, setIsRefundVisible] = useState(false);
  const [selectedReason, setSelectedReason] = useState<number | null>(null);

  useLoad(() => {
    console.log('Page loaded.');
  });

  return (
    <View className='min-h-screen bg-primary-linear'>
      <CustomHeader title='订单详情' bgColor='transparent' />

      <View className='p-16'>
        {/* 产品详情卡片 */}
        <View className='bg-white rounded-12 p-16 shadow-sm mb-12'>
          <View className='mb-8 flex items-center justify-between'>
            <Text className='text-20 font-semibold text-1D1F20'>产品详情</Text>

            <View className=' bg-FAF2F0 py-2 px-4 rounded-4'>
              <Text className='text-12 text-F84F2A'>有退改</Text>
            </View>
          </View>

          <View className='mb-8'>
            <Text className='text-16 text-23242D font-semibold'>蓝色气流成都跳伞</Text>
          </View>

          <View className='mb-8'>
            <Text className='text-14 text-4F5170 font-medium'>跳伞+手持摄像+第三方摄像</Text>
          </View>

          <View className=''>
            <Text className='text-12 text-9AA2CA'>游玩日期: 2022-08-29</Text>
          </View>
        </View>

        {/* 使用规则 */}
        <View className='bg-white rounded-12 p-16 shadow-sm'>
          <View className='text-16 font-bold text-1D1F20 mb-12'>使用规则</View>

          {/* 规则列表 */}
          {[
            { title: '有条件退改', hasArrow: true },
            { title: '已使用不支持退改', hasArrow: true },
          ].map((item, index) => (
            <View
              key={index}
              className={cx('flex items-center justify-between', {
                'mt-8': index !== 0,
              })}
            >
              <Text className='text-14 text-4F5170'>{item.title}</Text>
              {item.hasArrow && <Image src={arrowRightSLine} className='w-14 h-14' />}
            </View>
          ))}
        </View>

        {/* 出行人信息 */}
        <View className='mt-12 p-16 bg-white rounded-12 shadow-sm'>
          <View className='mb-12 flex items-center justify-between'>
            <Text className='text-16 font-bold text-1D1F20'>出行人信息</Text>
            <View
              className='flex items-center'
              onClick={() => {
                // 处理查看隐藏信息的点击事件
                console.log('查看隐藏信息');
              }}
            >
              <Text className='text-12 text-4F5170 mr-4'>查看隐藏信息</Text>
              <Image src={arrowRightSLine} className='w-14 h-14' />
            </View>
          </View>

          <View className='bg-F2F6FC py-12 px-16 rounded-12'>
            <View className='flex items-center mb-12'>
              <Text className='text-16 text-1D1F20 mr-8'>安妮</Text>
              <Text className='text-14 text-4F5170'>成人</Text>
            </View>

            <View className='mb-12'>
              <Text className='text-12 text-666666'>身份证号：</Text>
              <Text className='text-12 text-666666'>5138**********029</Text>
            </View>

            <View>
              <Text className='text-12 text-666666'>联系电话：</Text>
              <Text className='text-12 text-666666'>173-1231-3102</Text>
            </View>
          </View>
        </View>

        {/* 在线客服 */}
        <OnlineService className='mt-12' />

        <View className='mt-12 bg-white rounded-12 shadow-sm p-16'>
          {/* 支付金额 */}
          <View className='flex items-center justify-between border-b-1 border-E6EAF0 pb-12'>
            <Text className='text-14 text-4F5170'>支付金额:</Text>
            <View className='flex items-end'>
              <Text className='text-12 text-9AA2CA mr-2'>合计¥</Text>
              <Text className='text-16 font-semibold text-23242D'>1070</Text>
            </View>
          </View>

          {/* 订单信息 */}
          <View className='pt-12'>
            <View className='text-14 font-medium text-23242D mb-8'>订单信息</View>

            <View className=''>
              {[
                { label: '订单号:', value: '1562 7827 6157 9064 753' },
                { label: '交易流水号:', value: '123610868125127418715071' },
                { label: '创建时间:', value: '2024-04-20 14:18:12' },
                { label: '付款时间:', value: '2024-04-20 14:20:12' },
              ].map((item, index) => (
                <View
                  key={index}
                  className={cx('flex', {
                    'mt-8': index !== 0,
                  })}
                >
                  <View className='w-80 text-12 text-4F5170'>{item.label}</View>
                  <View className='flex-1 text-12 text-4F5170'>{item.value}</View>
                </View>
              ))}
            </View>
          </View>
        </View>
      </View>

      <BottomCtr className='flex items-center'>
        <View className='flex-1 px-6'>
          <Button
            className='h-36 bg-white rounded-4 border-1 border-00000014 text-primary text-14'
            onClick={() => {
              setIsRefundVisible(true);
            }}
          >
            退款
          </Button>
        </View>
        <View className='flex-1 px-6'>
          <Button
            className='h-36 bg-white rounded-4 border-1 border-00000014 text-primary text-14'
            onClick={() => {
              navigateTo({
                url: '/pages/orders/travelOrders/aerialTour/index',
              });
            }}
          >
            改期
          </Button>
        </View>
        <View className='flex-1 px-6'>
          <Button
            className='h-36 bg-white rounded-4 border-1 border-00000014 text-primary text-14'
            onClick={() => {
              navigateTo({
                url: '/pages/invoice/index',
              });
            }}
          >
            电子发票
          </Button>
        </View>
      </BottomCtr>

      <CustomActionSheet
        visible={isRefundVisible}
        title='请选择退款原因'
        onClose={() => setIsRefundVisible(false)}
      >
        <View className='pt-16 flex flex-wrap gap-12'>
          <View className='text-12 text-737578 font-medium '>
            当前订单未支付房费，取消成功后不会扣除任何费用
          </View>

          {REFUND_REASONS.map(reason => (
            <View
              key={reason.id}
              className={cx(
                'flex-shrink-0 h-36 flex items-center justify-center rounded-6',
                'w-reason-item ',
                {
                  'border-1 border-primary bg-white': reason.primary,
                  'bg-F7F8FA': !reason.primary,
                }
              )}
              onClick={() => {
                setSelectedReason(reason.id);
                setIsRefundVisible(false);
              }}
            >
              <Text
                className={cx('text-14', {
                  'text-primary': reason.primary,
                  'text-4F5170': !reason.primary,
                })}
              >
                {reason.text}
              </Text>
            </View>
          ))}

          <View className='flex items-center w-full pt-12'>
            <View className='flex-1 mr-10'>
              <Button className='rounded-12 h-48 bg-white text-16 text-primary font-semibold border-1 border-primary flex items-center justify-center'>
                取消
              </Button>
            </View>
            <View className='flex-1'>
              <Button
                className='rounded-12 h-48 bg-primary text-16 text-white font-semibold flex items-center justify-center'
                onClick={() => {
                  navigateTo({
                    url: '/pages/orders/travelOrders/refundDetails/index',
                  });
                }}
              >
                提交申请
              </Button>
            </View>
          </View>
        </View>
      </CustomActionSheet>
    </View>
  );
};

export default OrderDetails;
