import { useState } from 'react';
import cx from 'classnames';

import { View, Text, Button, Image } from '@tarojs/components';
import CustomHeader from '@/components/CustomHeader';
import BottomCtr from '@/components/BottomCtr';
import { arrowRightLine } from '@/utils/img';
import CustomActionSheet from '@/components/CustomActionSheet';

const AERIAL_TOUR_REASONS = [
  { id: 1, text: '出行计划有变', primary: true },
  { id: 2, text: '订错/多订' },
  { id: 3, text: '其他渠道更优惠' },
  { id: 4, text: '身体不适' },
  { id: 5, text: '无法入住' },
  { id: 6, text: '酒店加价' },
  { id: 7, text: '对酒店不满意' },
  { id: 8, text: '交通停运/延误' },
  { id: 9, text: '自然灾害' },
];

const AerialTour = () => {
  const [visible, setVisible] = useState(false);
  return (
    <View className='min-h-screen bg-primary-linear'>
      <CustomHeader showBackButton title='改期' bgColor='transparent' />

      <View className='py-12 px-16'>
        {/* 改期产品信息 */}
        <View className='bg-white rounded-12 p-16 shadow-sm mb-12'>
          <View className='mb-12'>
            <Text className='text-16 font-medium text-1D1F20'>改期产品</Text>
          </View>

          <View className='border-1 border-E6EAF0 rounded-8 p-16 mb-12'>
            <View className='text-16 text-23242D font-semibold mb-8'>蓝色气流成都跳伞</View>
            <View className='text-14 text-4F5170 mb-8'>跳伞+手持摄像+第三方摄像</View>
            <View className='text-12 text-9AA2CA'>游玩日期：2022-08-29</View>
          </View>

          <View className='bg-F84F2A1A rounded-8 py-12 px-16'>
            <Text className='text-12 text-F84F2A'>
              低空行不加收任何退改服务费，具体按航空公司规定收取，点击可查看
              <Text className='text-primary underline'>退改签规则＞</Text>
            </Text>
          </View>
        </View>

        {/* 改期日期选择 */}
        <View className='bg-white rounded-12 p-16 shadow-sm mb-12'>
          <View className='mb-12'>
            <Text className='text-16 font-medium text-1D1F20'>请选择改期日期</Text>
          </View>

          <View
            className='bg-F7F8FA rounded-8 py-12 px-16 flex items-center justify-between'
            onClick={() => {}}
          >
            <Text className='text-14 text-4F5170 font-medium'>2025年4月29日</Text>
            <Image src={arrowRightLine} className='w-18 h-18' />
          </View>
        </View>

        {/* 改期原因 */}
        <View className='bg-white rounded-12 p-16 shadow-sm mb-12'>
          <View className='mb-12'>
            <Text className='text-16 font-medium text-1D1F20'>请选择改期原因</Text>
          </View>

          <View
            className='bg-F7F8FA rounded-8 py-12 px-16 flex items-center justify-between'
            onClick={() => {
              setVisible(true);
            }}
          >
            <Text className='text-14 text-4F5170 font-medium'>我的计划有变，自愿改变行程计划</Text>
            <Image src={arrowRightLine} className='w-18 h-18' />
          </View>
        </View>

        {/* 温馨提示 */}
        <View className='bg-white rounded-12 p-16 shadow-sm mb-12'>
          <View className='mb-12'>
            <Text className='text-16 font-medium text-1D1F20'>温馨提示</Text>
          </View>

          <Text className='text-14 text-717C8C'>
            如果您的订单中包含多人，请分开提交改期申请。暂
            不支持儿童改期，如果您的订单中包含儿童乘客，请 您拨打出票方客服电话023-63560494办理
          </Text>
        </View>
      </View>

      <BottomCtr>
        <Button
          className='rounded-12 h-48 bg-primary text-16 text-white font-semibold flex items-center justify-center'
          onClick={() => {}}
        >
          下一步
        </Button>
      </BottomCtr>

      <CustomActionSheet visible={visible} title='请选择改期原因' onClose={() => setVisible(false)}>
        <View className='pt-16 flex flex-wrap gap-12'>
          {AERIAL_TOUR_REASONS.map(reason => (
            <View
              key={reason.id}
              className={cx(
                'flex-shrink-0 h-36 flex items-center justify-center rounded-6',
                'w-reason-item ',
                {
                  'border-1 border-primary bg-white': reason.primary,
                  'bg-F7F8FA': !reason.primary,
                }
              )}
              onClick={() => {
                setVisible(false);
              }}
            >
              <Text
                className={cx('text-14', {
                  'text-primary': reason.primary,
                  'text-4F5170': !reason.primary,
                })}
              >
                {reason.text}
              </Text>
            </View>
          ))}

          <View className='flex items-center w-full pt-12'>
            <View className='flex-1 mr-10'>
              <Button className='rounded-12 h-48 bg-white text-16 text-primary font-semibold border-1 border-primary flex items-center justify-center'>
                取消
              </Button>
            </View>
            <View className='flex-1'>
              <Button
                className='rounded-12 h-48 bg-primary text-16 text-white font-semibold flex items-center justify-center'
                onClick={() => {
                  // navigateTo({
                  //   url: '/pages/orders/travelOrders/refundDetails/index',
                  // });
                }}
              >
                确认提交
              </Button>
            </View>
          </View>
        </View>
      </CustomActionSheet>
    </View>
  );
};

export default AerialTour;
