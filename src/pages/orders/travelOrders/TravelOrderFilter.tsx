import { useState } from "react";
import cx from "classnames";
import { View, Text, Button } from "@tarojs/components";
import CustomCalendar from "@/components/CustomCalendar";

interface TravelOrderFilterProps {
  onReset?: () => void;
  onConfirm?: (filters: any) => void; // filters 类型根据实际需要定义
}

const TravelOrderFilter = ({ onReset, onConfirm }: TravelOrderFilterProps) => {
  const productTypes = [
    { id: "skydiving", label: "高空跳伞" },
    { id: "airTour", label: "空中游览" },
  ];
  const orderTypes = [
    { id: "pendingPayment", label: "待付款" },
    { id: "pendingUse", label: "待使用" },
    { id: "used", label: "已使用" },
    { id: "refunded", label: "有退改" },
  ];

  const [selectedProductType, setSelectedProductType] = useState("airTour");
  const [selectedOrderType, setSelectedOrderType] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("2024-04-20"); // 示例日期

  const handleReset = () => {
    setSelectedProductType("airTour");
    setSelectedOrderType("");
    setStartDate("");
    setEndDate("");
    onReset?.();
  };

  const handleConfirm = () => {
    onConfirm?.({
      productType: selectedProductType,
      orderType: selectedOrderType,
      startDate,
      endDate,
    });
  };

  const handleDateChange = (e, type: "start" | "end") => {
    const date = e.detail.value;
    if (type === "start") {
      setStartDate(date);
    } else {
      setEndDate(date);
    }
  };

  return (
    <View className="p-16">
      {/* Top */}
      <View className="pb-16">
        {/* 产品类型 */}
        <View className="mb-20">
          <View className="text-16 font-semibold text-1D1F20 mb-8">
            产品类型:
          </View>

          <View className="flex flex-wrap">
            {productTypes.map((type) => (
              <View>
                <Button
                  key={type.id}
                  className={cx(
                    "rounded-8 border-1 px-12 py-6 mr-8 mb-8 text-14 leading-normal",
                    {
                      "border-primary bg-primary-light text-primary":
                        selectedProductType === type.id, // 假设 primary-light 是浅蓝色背景
                      "border-E6EAF0 bg-white text-737578":
                        selectedProductType !== type.id,
                    }
                  )}
                  onClick={() => setSelectedProductType(type.id)}
                >
                  {type.label}
                </Button>
              </View>
            ))}
          </View>
        </View>

        {/* 订单类型 */}
        <View className="mb-20">
          <View className="text-16 font-semibold text-1D1F20 mb-8">
            订单类型:
          </View>

          <View className="flex flex-wrap">
            {orderTypes.map((type) => (
              <View>
                <Button
                  key={type.id}
                  className={cx(
                    "rounded-8 border-1 px-12 py-6 mr-8 mb-8 text-14 leading-normal",
                    {
                      "border-primary bg-primary-light text-primary":
                        selectedOrderType === type.id,
                      "border-E6EAF0 bg-white text-737578":
                        selectedOrderType !== type.id,
                    }
                  )}
                  onClick={() => setSelectedOrderType(type.id)}
                >
                  {type.label}
                </Button>
              </View>
            ))}
          </View>
        </View>

        {/* 预定时间 */}
        <View>
          <View className="text-16 font-semibold text-1D1F20 mb-8">
            预定时间:
          </View>

          <View className="flex items-center justify-between">
            <View className="bg-F2F6FC rounded-8 px-12 py-8 flex items-center justify-between flex-1">
              <Text
                className={cx(
                  "text-12",
                  startDate ? "text-1D1F20" : "text-C4C7CA"
                )}
              >
                {startDate || "请选择"}
              </Text>
              {/* <Image src={calendarIcon} className="w-16 h-16" /> */}
              <View className="w-16 h-16 bg-gray-300" /> {/* 临时占位符 */}
            </View>

            <Text className="mx-8">-</Text>

            <View className="bg-F2F6FC rounded-8 px-12 py-8 flex items-center justify-between flex-1">
              <Text
                className={cx(
                  "text-12",
                  endDate ? "text-1D1F20" : "text-C4C7CA"
                )}
              >
                {endDate || "请选择"}
              </Text>
              {/* <Image src={calendarIcon} className="w-16 h-16" /> */}
              <View className="w-16 h-16 bg-gray-300" /> {/* 临时占位符 */}
            </View>
          </View>
        </View>
      </View>

      {/* Bottom */}
      <View className="flex items-center pt-16">
        <View className="flex-1">
          <Button
            className="h-48 flex items-center justify-center rounded-12 border-1 border-primary text-primary text-16 font-semiBold mr-10 bg-white"
            onClick={handleReset}
          >
            重置
          </Button>
        </View>

        <View className="flex-1">
          <Button
            className="h-48 flex items-center justify-center rounded-12 bg-primary text-white text-16 font-semiBold"
            onClick={handleConfirm}
          >
            确定
          </Button>
        </View>
      </View>

      {/* <CustomCalendar
        customActionSheetProps={{
          visible: true,
        }}
      /> */}
    </View>
  );
};

export default TravelOrderFilter;
