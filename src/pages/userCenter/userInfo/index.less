@import "../../../styles/variables.less";
.user-info {
  height: 100vh;
  background: linear-gradient(
    180deg,
    rgba(22, 99, 248, 0.4) 0%,
    #f0f4fa 26.42%
  );
  .user-info-container {
    padding: 30px 16px 20px 16px;
    box-sizing: border-box;
  }
  .card-box {
    margin-bottom: 40px;
  }
  .user-info-btn {
    margin-bottom: 16px;
  }
}
.user-info-modal-body {
  padding: 10px;
  input {
    width: 100%;
    height: 40px;
    border: 1px solid #d3d8e5;
    border-radius: 6px;
    padding: 0 10px;
    box-sizing: border-box;
    margin: 10px 0 25px 0;
  }
  .modal-btn-box {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    .modal-btn {
      flex: 1;
    }
  }
}
