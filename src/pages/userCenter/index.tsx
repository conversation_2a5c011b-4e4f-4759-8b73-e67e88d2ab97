import { memo } from 'react';
import { Image, View } from '@tarojs/components';
import CustomHeader from '@/components/CustomHeader';
import './index.less';
import { _localStorage, toast, toUrl } from '@/utils';
import { TOKEN_NAME } from '@/utils/constant';
import { chatIcon, infoIcon, invoiceIcon, logo, myOrderIcon } from '@/utils/img';

const USER_MENU = [
  { name: '我的订单', icon: myOrderIcon, path: '' },
  { name: '常用信息', icon: infoIcon, path: '' },
  { name: '发票管理', icon: invoiceIcon, path: '' },
  { name: '客服中心', icon: chatIcon, path: '' },
];
const UserCenter = () => {
  const isLogin = _localStorage.getItem(TOKEN_NAME);

  const handleClick = item => {
    if (!isLogin) {
      toast.info('请先登录');
      return;
    }
    toUrl(item?.path);
  };
  return (
    <>
      <View className='user-center'>
        <CustomHeader
          showBackButton={false}
          bgColor={'transparent'}
          title={'我的'}
          titleColor={'#1D1F20'}
        />
        <View className='user-center-content'>
          <View className={'head-avatar-container'}>
            <View className={'head-avatar'}>
              <Image src={logo} />
            </View>
            <View className={'name-box'}>
              {isLogin ? (
                <View className={'name'} onClick={() => toUrl('/pages/userCenter/userInfo/index')}>
                  用户名
                </View>
              ) : (
                <View className={'name'} onClick={() => toUrl('/pages/login/index')}>
                  登录/注册
                </View>
              )}
              <View className={'welcome-text'}>欢迎来到低空行平台</View>
            </View>
          </View>
          <View className={'card-box'}>
            {USER_MENU.map(item => {
              return (
                <View className={'card-item'} key={item?.name} onClick={() => handleClick(item)}>
                  <Image src={item?.icon} />
                  <View className={'card-item-text'}>{item?.name}</View>
                </View>
              );
            })}
          </View>
        </View>
      </View>
    </>
  );
};
export default memo(UserCenter);
