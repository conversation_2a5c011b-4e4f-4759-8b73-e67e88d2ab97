import { memo, useState } from 'react';
import { View, Text, ScrollView, Image } from '@tarojs/components';
import { Collapse } from '@nutui/nutui-react-taro';
import CustomHeader from '@/components/CustomHeader';
import { toUrl } from '@/utils';
import './index.less';
import { ArrowDown, ArrowRight } from '@nutui/icons-react-taro';
import FlightItem from '@/pages/jauntAirLine/components/FlightItem';
import CustomTag from '@/components/CustomTag';
import { packetIcon, ticketIcon } from '@/utils/img';

interface CabinInfo {
  id: string;
  name: string;
  price: number;
  features: string[];
}

const SelectCabin = ({ flightId }: { flightId: string | number }) => {
  const [selectedCabin, setSelectedCabin] = useState<string | null>(null);
  const [cabins] = useState<CabinInfo[]>([
    {
      id: '1',
      name: '经济舱',
      price: 1070,
      features: ['标准座椅', '15kg行李额', '免费茶水'],
    },
    {
      id: '2',
      name: '商务舱',
      price: 1570,
      features: ['宽敞座椅', '25kg行李额', '免费餐食', '优先登机'],
    },
    {
      id: '3',
      name: '头等舱',
      price: 2070,
      features: ['豪华座椅', '35kg行李额', '免费餐食', '优先登机', '专属服务'],
    },
  ]);
  const [flightInfo, setFlightInfo] = useState<any>({
    id: '3',
    flightNo: '3U6099',
    departureCity: '成都',
    arrivalCity: '绵阳',
    departureAirport: '双流国际机场',
    arrivalAirport: '南郊机场',
    departureTerminal: 'T1',
    arrivalTerminal: 'T1',
    departureTime: '16:00',
    arrivalTime: '17:50',
    duration: '1h50m',
    price: 1150,
    aircraft: '空客H135',
    airline: '川航',
    isDirect: true,
  });

  const handleSelectCabin = (cabinId: string) => {
    setSelectedCabin(cabinId);
  };

  const handleContinue = () => {
    toUrl(`/pages/jauntAirLine/passengerInfo/index?cabinId=${selectedCabin}`);
  };

  return (
    <View className='select-cabin-page'>
      <CustomHeader showBackButton={true} bgColor={'transparent'} title={'选择舱位'} />
      <View className='card-box flight-info-card'>
        <Collapse defaultActiveName={['1', '2']} expandIcon={<ArrowDown />}>
          <Collapse.Item
            title={
              <View className={'flight-info-title'}>
                <CustomTag type={'gray'}>出发</CustomTag>
                <Text>4月20日 周三</Text>
                <View className={'split-line'} />
                <View>
                  {flightInfo?.departureCity} - {flightInfo?.arrivalCity}
                </View>
              </View>
            }
          >
            <FlightItem flight={flightInfo} />
          </Collapse.Item>
        </Collapse>
      </View>
      <ScrollView className='scrollview' scrollY scrollWithAnimation enableBackToTop={true}>
        <View className='cabin-selection-container'>
          {cabins.map(cabin => (
            <View
              className={`card-box cabin-item`}
              key={cabin.id}
              onClick={() => handleSelectCabin(cabin.id)}
            >
              <View className={'cabin-left'}>
                <View className={'cabin-header'}>
                  <Text className={'cabin-name'}>{cabin.name}</Text>
                  <Text className={'cabin-short-name'}>Y舱</Text>
                  <CustomTag bgColor={'rgba(248, 190, 42, 0.2)'} color={'#1D1F20'}>
                    2.2折
                  </CustomTag>
                </View>
                <View className={'cabin-features'}>
                  <Image src={packetIcon} />
                  <Text>行李：</Text>
                  <Text>手提5kg</Text>
                  <View className={'split-line'} />
                  <Text>托运5kg</Text>
                </View>
                <View className={'cabin-features'}>
                  <Image src={ticketIcon} />
                  <Text>退改：</Text>
                  <Text>0元起</Text>
                  <ArrowRight size={12} />
                </View>
              </View>
              <View className={'cabin-right'} onClick={() => handleContinue()}>
                <View className={'price-box'}>
                  ¥<Text>580</Text>起
                </View>
                <View className={'select-btn'}>订</View>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default memo(SelectCabin);
