import { memo, useState } from 'react';
import { View, Text, ScrollView } from '@tarojs/components';
import { Button, Checkbox, Collapse, Radio } from '@nutui/nutui-react-taro';
import CustomHeader from '@/components/CustomHeader';
import { toUrl } from '@/utils';
import './index.less';
import '../selectCabin/index.less';
import { ArrowDown, ArrowRight, Plus } from '@nutui/icons-react-taro';
import BottomCtr from '@/components/BottomCtr';
import CustomTag from '@/components/CustomTag';
import FlightItem from '@/pages/jauntAirLine/components/FlightItem';

interface PassengerInfo {
  id: string;
  name: string;
  idType: string;
  idNumber: string;
  selected?: boolean;
}

const PassengerInfo = () => {
  // 航班信息
  const flightInfo = {
    id: '3',
    flightNo: '3U6099',
    departureCity: '成都',
    arrivalCity: '绵阳',
    departureAirport: '双流国际机场',
    arrivalAirport: '南郊机场',
    departureTerminal: 'T1',
    arrivalTerminal: 'T1',
    departureTime: '16:00',
    arrivalTime: '17:50',
    duration: '1h50m',
    price: 1150,
    aircraft: '空客H135',
    airline: '川航',
    isDirect: true,
  };

  // 乘机人列表
  const [passengers, setPassengers] = useState<PassengerInfo[]>([
    {
      id: '1',
      name: '安妮',
      idType: 'ID_CARD',
      idNumber: '5139**********048',
      selected: true,
    },
    {
      id: '2',
      name: '安小妮',
      idType: 'ID_CARD',
      idNumber: '5139**********048',
      selected: true,
    },
    {
      id: '3',
      name: '安三妮',
      idType: 'ID_CARD',
      idNumber: '',
      selected: false,
    },
  ]);

  // 联系人信息
  const [contactInfo, setContactInfo] = useState({
    name: '安小妮',
    phone: '173*******3102',
  });

  // 保险选择
  const [selectedInsurance, setSelectedInsurance] = useState('保障险');

  // 协议勾选
  const [agreementChecked, setAgreementChecked] = useState(true);

  // 总价
  const [totalPrice, setTotalPrice] = useState(870);

  // 优惠金额
  const [discountAmount, setDiscountAmount] = useState(80);

  // 选择乘机人
  const togglePassengerSelection = (id: string) => {
    setPassengers(passengers.map(p => (p.id === id ? { ...p, selected: !p.selected } : p)));
  };

  // 添加乘机人
  const handleAddPassenger = () => {
    if (passengers.length < 9) {
      setPassengers([
        ...passengers,
        {
          id: `${passengers.length + 1}`,
          name: '',
          idType: 'ID_CARD',
          idNumber: '',
          selected: false,
        },
      ]);
    }
  };

  // 编辑乘机人信息
  const editPassenger = (id: string) => {
    console.log('编辑乘机人', id);
    // 这里可以跳转到编辑页面或者弹出编辑弹窗
  };

  // 编辑联系人信息
  const editContactInfo = () => {
    console.log('编辑联系人信息');
    // 这里可以跳转到编辑页面或者弹出编辑弹窗
  };

  // 表单验证
  const isFormValid = () => {
    // 至少选择一个乘机人
    const hasSelectedPassenger = passengers.some(p => p.selected);

    // 联系人信息完整
    const hasContactInfo = contactInfo.name && contactInfo.phone;

    // 同意协议
    return hasSelectedPassenger && hasContactInfo && agreementChecked;
  };

  // 提交订单
  const handleSubmitOrder = () => {
    if (isFormValid()) {
      toUrl('/pages/jauntAirLine/payment/index');
    }
  };

  return (
    <View className='select-cabin-page passenger-info-page '>
      <CustomHeader showBackButton={true} bgColor={'transparent'} title={'旅客信息'} />
      <View className='card-box flight-info-card'>
        <Collapse defaultActiveName={['1', '2']} expandIcon={<ArrowDown />}>
          <Collapse.Item
            title={
              <View className={'flight-info-title'}>
                <CustomTag type={'gray'}>出发</CustomTag>
                <Text>4月20日 周三</Text>
                <View className={'split-line'} />
                <View>
                  {flightInfo?.departureCity} - {flightInfo?.arrivalCity}
                </View>
              </View>
            }
          >
            <FlightItem flight={flightInfo} />
          </Collapse.Item>
        </Collapse>
      </View>
      <ScrollView className='scrollview' scrollY scrollWithAnimation enableBackToTop={true}>
        {/* 乘机人选择 */}
        <View className='card-box passenger-section'>
          <View className='section-title'>乘机人</View>
          <View className='passenger-selector'>
            {passengers.map(passenger => (
              <View
                key={passenger.id}
                className={`passenger-tag ${passenger.selected ? 'selected' : ''}`}
                onClick={() => togglePassengerSelection(passenger.id)}
              >
                {/*<Radio checked={passenger.selected} icon={<Check color='#fff' />} />*/}
                <Text>{passenger.name}</Text>
              </View>
            ))}
            <View className='add-passenger-btn' onClick={handleAddPassenger}>
              <Plus color='#1663F8' size={16} />
              <Text>新增乘机人</Text>
            </View>
          </View>

          {/* 已选乘机人信息 */}
          {passengers
            .filter(p => p.selected)
            .map(passenger => (
              <View
                key={passenger.id}
                className='passenger-info-item'
                onClick={() => editPassenger(passenger.id)}
              >
                <View className='passenger-info-left'>
                  <Text className='passenger-name'>{passenger.name}</Text>
                  <Text className='passenger-count'>{passenger.selected ? '成人' : ''}</Text>
                </View>
                <View className='passenger-info-right'>
                  <Text className='id-info'>身份证：{passenger.idNumber}</Text>
                  <ArrowRight color='#999' size={16} />
                </View>
              </View>
            ))}
        </View>

        {/* 联系电话 */}
        <View className='card-box contact-phone-section'>
          <Text className='contact-phone-label'>联系电话</Text>
          <Text className='contact-phone-value'>+86 {contactInfo.phone}</Text>
        </View>

        {/* 联系人信息 */}
        <View className='card-box contact-info-section'>
          <View className='section-title'>联系人信息</View>
          <Text className='section-subtitle'>用于接收航班信息</Text>

          <View className='contact-info-item' onClick={editContactInfo}>
            <View className='contact-info-left'>
              <Text className='contact-label'>姓名</Text>
              <Text className='contact-value'>{contactInfo.name}</Text>
            </View>
            <View className='edit-icon'>
              <ArrowRight color='#999' size={16} />
            </View>
          </View>

          <View className='contact-info-item' onClick={editContactInfo}>
            <View className='contact-info-left'>
              <Text className='contact-label'>联系电话</Text>
              <Text className='contact-value'>+86 {contactInfo.phone}</Text>
            </View>
            <View className='edit-icon'>
              <ArrowRight color='#999' size={16} />
            </View>
          </View>
        </View>

        {/* 航空保险 */}
        <View className='card-box insurance-section'>
          <View className='section-title'>航空保险</View>
          <Text className='section-subtitle'>出行有保障，人人更安心</Text>

          <View className='insurance-options'>
            <View
              className={`insurance-card ${selectedInsurance === '保障险' ? 'selected' : ''}`}
              onClick={() => setSelectedInsurance('保障险')}
            >
              <View className='insurance-header'>
                <View className='insurance-left'>
                  <Text className='insurance-name'>保障险</Text>
                  <Text className='insurance-price'>¥39/人</Text>
                </View>
                <Radio checked={selectedInsurance === '保障险'} />
              </View>
              <View className='insurance-details'>
                <Text className='insurance-coverage'>航意险额度 ¥200万</Text>
                <Text className='insurance-coverage'>延误险额度 ¥100</Text>
              </View>
            </View>

            <View
              className={`insurance-card ${selectedInsurance === '意外险' ? 'selected' : ''}`}
              onClick={() => setSelectedInsurance('意外险')}
            >
              <View className='insurance-header'>
                <View className='insurance-left'>
                  <Text className='insurance-name'>意外险</Text>
                  <Text className='insurance-price'>¥55/人</Text>
                </View>
                <Radio checked={selectedInsurance === '意外险'} />
              </View>
              <View className='insurance-details'>
                <Text className='insurance-coverage'>航意险额度 ¥400万</Text>
                <Text className='insurance-coverage'>延误险额度 ¥600</Text>
              </View>
            </View>

            <View
              className={`insurance-card ${selectedInsurance === '综合险' ? 'selected' : ''}`}
              onClick={() => setSelectedInsurance('综合险')}
            >
              <View className='insurance-header'>
                <View className='insurance-left'>
                  <Text className='insurance-name'>综合险</Text>
                  <Text className='insurance-price'>¥72/人</Text>
                </View>
                <Radio checked={selectedInsurance === '综合险'} />
              </View>
              <View className='insurance-details'>
                <Text className='insurance-coverage'>航意险额度 ¥600万</Text>
                <Text className='insurance-coverage'>延误险额度 ¥900</Text>
              </View>
            </View>
          </View>
        </View>

        {/* 协议勾选 */}
        <View className='agreement-section'>
          <Checkbox checked={agreementChecked} onChange={val => setAgreementChecked(val)}>
            <Text className='agreement-text'>我已确认乘客姓名，且认真阅读并同意</Text>
          </Checkbox>
          <View className='agreement-links'>
            <Text className='agreement-link'>《中国内地旅客须知》</Text>
            <Text className='agreement-link'>《东航无障碍服务》</Text>
            <Text className='agreement-link'>《东航旅客及行李运输规定》</Text>
            <Text className='agreement-link'>《购票须知》</Text>
            <Text className='agreement-link'>《特殊旅客须知》</Text>
          </View>
        </View>

        {/* 卡券优惠 */}
      </ScrollView>

      {/* 底部操作栏 */}
      <BottomCtr>
        <View className='discount-section' onClick={() => console.log('选择优惠券')}>
          <Text className='discount-label'>卡券优惠</Text>
          <View className='discount-value'>
            <Text className='discount-amount'>-¥{discountAmount}</Text>
            <ArrowRight color='#999' size={16} />
          </View>
        </View>
        <View className='bottom-action'>
          <View className='price-info'>
            <Text className='price-label'>¥</Text>
            <Text className='price-value'>{totalPrice}</Text>
            <Text className='price-detail'>明细</Text>
          </View>
          <Button
            className={`submit-btn ${!isFormValid() ? 'disabled' : ''}`}
            disabled={!isFormValid()}
            onClick={handleSubmitOrder}
          >
            提交订单
          </Button>
        </View>
      </BottomCtr>
    </View>
  );
};

export default memo(PassengerInfo);
