import { memo, useState, useEffect } from 'react';
import { View, Image } from '@tarojs/components';
import dayjs from 'dayjs';
import './index.less';
import { getWeekDay } from '@/utils';
import { calenderIconBlue } from '@/utils/img';
import { DATE_FORMAT, SUCCESS_CODE } from '@/utils/constant';
import api from '@/api';
import PriceCalendarItem from './components/PriceCalendarItem';
import CalendarModal from './components/CalendarModal';

interface PriceCalendarData {
  date: string;
  day: string;
  price: number;
  fullDate: string; // YYYY-MM-DD格式
}

interface PriceCalendarProps {
  onDateSelect: (date: string, day: string) => void;
  selectedDate?: string;
  depCode?: string; // 出发地三字码
  arrCode?: string; // 到达地三字码
}

const PriceCalendar = ({ onDateSelect, selectedDate, depCode, arrCode }: PriceCalendarProps) => {
  // 价格日历数据
  const [priceCalendar, setPriceCalendar] = useState<PriceCalendarData[]>([]);
  // 当前选中的日期
  const [currentSelectedDate, setCurrentSelectedDate] = useState(
    selectedDate || dayjs().format('M-D')
  );
  // 日历弹窗显示状态
  const [showCalendarModal, setShowCalendarModal] = useState(false);
  // 加载状态
  const [loading, setLoading] = useState(false);

  // 格式化日期为 "M-D" 格式
  const formatDateToMD = (date: string) => {
    const dayObj = dayjs(date);
    return `${dayObj.month() + 1}-${dayObj.date()}`;
  };

  // 获取价格日历数据
  const fetchCalendarData = async () => {
    setLoading(true);

    const startDate = dayjs().format(DATE_FORMAT);
    const endDate = dayjs().add(2, 'day').format(DATE_FORMAT);

    const { code, data } = await api.shortRouteV10CalendarCreate({
      startDate,
      endDate,
      depCode,
      arrCode,
    });
    console.log(data);

    if (code === SUCCESS_CODE) {
      const calendarData = data?.map(item => ({
        date: formatDateToMD(item.flightDate!),
        day: getWeekDay(formatDateToMD(item.flightDate!)),
        price: item.price || 0,
        fullDate: item.flightDate!,
      }));
      setPriceCalendar(calendarData);
    } else {
      generateDefaultData();
    }

    setLoading(false);
  };

  // 生成默认数据
  const generateDefaultData = () => {
    const today = dayjs();
    const defaultData: PriceCalendarData[] = [];

    for (let i = 0; i < 3; i++) {
      const date = today.add(i, 'day');
      const dateStr = formatDateToMD(date.format(DATE_FORMAT));
      defaultData.push({
        date: dateStr,
        day: getWeekDay(dateStr),
        price: 980 + i * 90, // 默认价格递增
        fullDate: date.format(DATE_FORMAT),
      });
    }

    setPriceCalendar(defaultData);
  };

  // 当外部selectedDate变化时更新内部状态
  useEffect(() => {
    if (selectedDate) {
      setCurrentSelectedDate(selectedDate);
    }
  }, [selectedDate]);

  // 当出发地或到达地变化时重新获取数据
  useEffect(() => {
    fetchCalendarData();
  }, []);

  // 处理日期选择
  const handleDateSelect = (item: PriceCalendarData) => {
    setCurrentSelectedDate(item.date);
    onDateSelect(item.date, item.day);
  };

  // 处理日历弹窗中的日期选择
  const handleCalendarSelect = (date: string) => {
    const dateStr = formatDateToMD(date);
    const dayStr = getWeekDay(dateStr);
    setCurrentSelectedDate(dateStr);
    onDateSelect(dateStr, dayStr);
    setShowCalendarModal(false);
  };

  return (
    <>
      <View className='price-calendar'>
        {priceCalendar.map(item => (
          <PriceCalendarItem
            key={item.date}
            data={item}
            selected={currentSelectedDate === item.date}
            loading={loading}
            onClick={() => handleDateSelect(item)}
          />
        ))}
        <View className='calendar-more' onClick={() => setShowCalendarModal(true)}>
          <Image src={calenderIconBlue} />
        </View>
      </View>

      <CalendarModal
        visible={showCalendarModal}
        onClose={() => setShowCalendarModal(false)}
        onConfirm={handleCalendarSelect}
        selectedDate={currentSelectedDate}
      />
    </>
  );
};

export default memo(PriceCalendar);
