import { memo, useState, useEffect } from 'react';
import { View, Text, Image } from '@tarojs/components';
import './index.less';
import { getWeekDay } from '@/utils';
import { calenderIconBlue } from '@/utils/img';

interface PriceCalendarItem {
  date: string;
  day: string;
  price: number;
}

interface PriceCalendarProps {
  onDateSelect: (date: string, day: string) => void;
  selectedDate?: string;
}

const PriceCalendar = ({ onDateSelect, selectedDate }: PriceCalendarProps) => {
  // 格式化日期为 "x月x日" 格式
  const formatDate = (date: Date) => `${date.getMonth() + 1}-${date.getDate()}`;

  // 价格日历数据
  const [priceCalendar, _setPriceCalendar] = useState<PriceCalendarItem[]>(() => {
    // 获取当前日期
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    return [
      {
        date: formatDate(yesterday),
        day: getWeekDay(formatDate(yesterday)),
        price: 980,
      },
      {
        date: formatDate(today),
        day: getWeekDay(formatDate(today)),
        price: 1070,
      },
      {
        date: formatDate(tomorrow),
        day: getWeekDay(formatDate(tomorrow)),
        price: 1150,
      },
    ];
  });

  // 当前选中的日期
  const [currentSelectedDate, setCurrentSelectedDate] = useState(
    selectedDate || formatDate(new Date())
  );

  // 当外部selectedDate变化时更新内部状态
  useEffect(() => {
    if (selectedDate) {
      setCurrentSelectedDate(selectedDate);
    }
  }, [selectedDate]);

  // 处理日期选择
  const handleDateSelect = (item: PriceCalendarItem) => {
    setCurrentSelectedDate(item.date);
    onDateSelect(item.date, item.day);
  };

  return (
    <View className='price-calendar'>
      {priceCalendar.map(item => (
        <View
          key={item.date}
          className={`calendar-item ${currentSelectedDate === item.date ? 'selected' : ''}`}
          onClick={() => handleDateSelect(item)}
        >
          <Text className='calendar-date'>{`${item.date.split('-')[0]}月${
            item.date.split('-')[1]
          }日`}</Text>
          <Text className='calendar-day'>{item.day}</Text>
          <Text className='calendar-price'>¥{item.price}</Text>
        </View>
      ))}
      <View className={'calendar-more'}>
        <Image src={calenderIconBlue} />
      </View>
    </View>
  );
};

export default memo(PriceCalendar);
