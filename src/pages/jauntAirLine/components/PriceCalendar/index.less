@color-blue: #1663f8;
@color-red: #f84f2a;
@color-gray: #999;

.price-calendar {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  height: 86px;
  border-bottom: 1px solid #e7e7e7;
  color: #1c1c1c;

  .calendar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
    position: relative;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(72, 129, 226, 0.05);
    }

    &.selected {
      background: rgba(72, 129, 226, 0.1);

      .calendar-date,
      .calendar-day {
        color: @color-blue;
        font-weight: 600;
      }
    }

    &.loading {
      opacity: 0.6;

      .calendar-price {
        color: @color-gray;
      }
    }

    .calendar-date {
      font-size: 16px;
      margin-bottom: 4px;
      font-weight: 500;
    }

    .calendar-day {
      font-size: 12px;
      margin-bottom: 4px;
      color: @color-gray;
    }

    .calendar-price {
      font-size: 14px;
      color: @color-blue;
      font-weight: 600;
    }
  }

  .calendar-more {
    width: 36px;
    height: 100%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-left: 1px solid #e7e7e7;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(72, 129, 226, 0.05);
    }

    image {
      width: 16px;
      height: 16px;
    }
  }
}

// 日历弹窗样式
.calendar-modal {
  .nut-calendarcard {
    border: none;
    box-shadow: none;
  }

  .nut-calendarcard-header {
    color: #191919;
    font-weight: 600;
    font-size: 14px;
  }

  .nut-calendarcard-day {
    .day-view {
      color: #191919;
      font-weight: 600;
      font-size: 14px;
    }
  }

  .nut-calendarcard-day.active {
    background-color: @color-blue;

    .day-view,
    .day-bottom-view {
      color: white;
    }
  }

  .nut-calendarcard-day.disabled {
    opacity: 0.3;
  }

  .nut-calendarcard-day.next,
  .nut-calendarcard-day.prev {
    opacity: 0.3;
  }

  .nut-calendarcard-day.header {
    color: #191919;
    font-weight: 600;
    font-size: 14px;
  }

  .nut-calendarcard-day.header.weekend {
    color: @color-blue;
  }
}
