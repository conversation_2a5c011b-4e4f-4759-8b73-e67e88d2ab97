import { memo } from 'react';
import { View, Text } from '@tarojs/components';

interface PriceCalendarData {
  date: string;
  day: string;
  price: number;
  fullDate: string;
}

interface PriceCalendarItemProps {
  data: PriceCalendarData;
  selected: boolean;
  loading: boolean;
  onClick: () => void;
}

const PriceCalendarItem = ({ data, selected, loading, onClick }: PriceCalendarItemProps) => {
  return (
    <View
      className={`calendar-item ${selected ? 'selected' : ''} ${loading ? 'loading' : ''}`}
      onClick={onClick}
    >
      <Text className='calendar-date'>
        {`${data.date.split('-')[0]}月${data.date.split('-')[1]}日`}
      </Text>
      <Text className='calendar-day'>{data.day}</Text>
      <Text className='calendar-price'>
        {loading ? '...' : `¥${data.price}`}
      </Text>
    </View>
  );
};

export default memo(PriceCalendarItem);
