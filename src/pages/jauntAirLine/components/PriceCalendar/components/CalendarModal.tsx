import { memo } from 'react';
import { View } from '@tarojs/components';
import { CalendarCard, CalendarCardDay } from '@nutui/nutui-react-taro';
import CustomActionSheet from '@/components/CustomActionSheet';
import dayjs from 'dayjs';
import { DATE_FORMAT } from '@/utils/constant';

interface CalendarModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (date: string) => void;
  selectedDate?: string;
}

const CalendarModal = ({ visible, onClose, onConfirm }: CalendarModalProps) => {
  const handleDateChange = (val: any) => {
    const dateStr = dayjs(val).format(DATE_FORMAT);
    onConfirm(dateStr);
  };

  const disableDay = (day: CalendarCardDay) => {
    // 禁用已经过去的日期
    const dateStr = `${day.year}-${day.month.toString().padStart(2, '0')}-${day.date.toString().padStart(2, '0')}`;
    const today = dayjs().startOf('day');
    return dayjs(dateStr).isBefore(today);
  };

  const renderDayBottom = () => {
    // 这里可以根据需要显示价格信息
    // 暂时返回空，后续可以扩展
    return null;
  };

  return (
    <CustomActionSheet
      visible={visible}
      title="选择日期"
      onCancel={onClose}
      onConfirm={() => {
        // CalendarCard的onChange会处理确认逻辑
      }}
    >
      <View className="calendar-modal">
        <CalendarCard
          onChange={handleDateChange}
          disableDay={disableDay}
          renderDayBottom={renderDayBottom}
          startDate={new Date()}
          endDate={dayjs().add(3, 'month').toDate()}
        />
      </View>
    </CustomActionSheet>
  );
};

export default memo(CalendarModal);
