import { memo } from 'react';
import { View, Text, Image } from '@tarojs/components';
import { jaunt } from '@/utils/img';
import './index.less';

interface FlightItemProps {
  flight: {
    id: string;
    flightNo: string;
    departureCity: string;
    arrivalCity: string;
    departureAirport: string;
    arrivalAirport: string;
    departureTerminal: string;
    arrivalTerminal: string;
    departureTime: string;
    arrivalTime: string;
    duration: string;
    price: number;
    aircraft: string;
    airline: string;
    isDirect: boolean;
    stopInfo?: string;
  };
}

const FlightItem = ({ flight }: FlightItemProps) => {
  return (
    <View className='card-box flight-info-section'>
      <View className='time-section'>
        <View className='departure-info'>
          <Text className='time'>{flight.departureTime}</Text>
          <Text className='airport'>
            {flight.departureAirport}
            {flight.departureTerminal}
          </Text>
        </View>

        <View className='flight-duration'>
          <Text className='duration'>{flight.duration}</Text>
          <View className='flight-line'></View>
          <Text className='stop-info'>{flight.isDirect ? '直达' : `经停${flight.stopInfo}`}</Text>
        </View>

        <View className='arrival-info'>
          <Text className='time'>{flight.arrivalTime}</Text>
          <Text className='airport'>
            {flight.arrivalAirport}
            {flight.arrivalTerminal}
          </Text>
        </View>
      </View>

      <View className='flight-detail'>
        <View className={'left'}>
          <Text className='flight-no'>{flight.flightNo}</Text>
          <View className='divider'></View>
          <Text className='aircraft'>{flight.aircraft}</Text>
        </View>
        <View>
          <Image src={jaunt} className='airline-logo' />
        </View>
      </View>
    </View>
  );
};

export default memo(FlightItem);
