@color-blue: #1663f8;
@color-red: #f84f2a;

.card-box {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
}

.flight-info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .time-section {
    display: flex;
    align-items: flex-start;

    .departure-info,
    .arrival-info {
      display: flex;
      flex-direction: column;

      .time {
        font-size: 20px;
        font-weight: bold;
        color: #1d1f20;
        margin-bottom: 4px;
      }

      .airport {
        font-size: 12px;
        color: rgba(28, 28, 28, 0.8);
      }
    }

    .flight-duration {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 0 12px;

      .duration {
        font-size: 12px;
        color: #737578;
        margin-bottom: 4px;
      }

      .flight-line {
        width: 100%;
        height: 1px;
        background: rgba(28, 28, 28, 0.1);
      }

      .stop-info {
        font-size: 12px;
        color: rgba(28, 28, 28, 0.8);
        margin-top: 4px;
      }
    }
  }

  .flight-detail {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 8px;

    .left {
      display: inline-flex;
      align-items: center;
    }

    .flight-no {
      font-size: 12px;
      color: #737578;
    }

    .divider {
      width: 1px;
      height: 12px;
      background-color: #737578;
      margin: 0 4px;
    }

    .aircraft {
      font-size: 12px;
      color: #737578;
    }

    .airline-logo {
      width: 16px;
      height: 16px;
      margin-left: 8px;
      border-radius: 50%;
    }
  }
}
