import { memo, useState } from "react";
import CustomHeader from "../../components/CustomHeader";
import { Button, View, Text, Image } from "@tarojs/components";
import "./index.less";
import LoginProtocol from "@/components/LoginProtocol";
import { toast, toUrl } from "@/utils";
import { loginBgLight, logo } from "@/utils/img";

const LoginPage = () => {
  const [isAgree, setIsAgree] = useState(false);
  //获取手机号
  const getPhoneNumber = (e) => {
    const { code } = e.detail;
    console.log(code);
    if (code) {
      //登录的方法
    }
  };
  // 一键登录
  const onLogin = () => {
    if (!isAgree) {
      toast.info("请阅读并同意遵守低空行平台《服务协议》《隐私政策》");
      return;
    }
  };
  return (
    <View className="login-page">
      <CustomHeader
        showBackButton={true}
        bgColor={"transparent"}
        backIconColor={"#323232"}
      />
      <View className={"login-bg"}>
        <Image src={loginBgLight} />
      </View>
      <View className={"login-content"}>
        <Image src={logo} className={"logo"} />
        <View className={"welcome-text"}>
          欢迎使用<Text>低空行平台</Text>
        </View>
        <View className={"auth-msg"}>
          为了提供更优质服务，需要授权你的公开信息
        </View>
        <Button
          className={"wx-login-btn"}
          openType={isAgree ? "getPhoneNumber" : undefined}
          onGetPhoneNumber={getPhoneNumber}
          onClick={onLogin}
        >
          微信一键登录
        </Button>
        <LoginProtocol
          getData={(checked) => {
            setIsAgree(checked);
          }}
        />
        <View
          className={"ms-code-text"}
          onClick={() => toUrl("/pages/verCodeLogin/index")}
        >
          短信验证码登录
        </View>
      </View>
    </View>
  );
};
export default memo(LoginPage);
