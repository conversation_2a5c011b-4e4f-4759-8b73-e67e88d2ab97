@import '../../styles/variables.less';
.home-page {
  height: 100vh;
  width: 100%;
  padding: 0 16px;
  box-sizing: border-box;
  background: linear-gradient(180deg, #1663f8 0%, #f5f3f3 45%);
  .swapper-box {
    width: 100%;
    height: 146px;
    margin: 10px auto;
    border-radius: 12px;
    overflow: hidden;
    .swiper-bg {
      width: 100%;
      height: 100%;
      //object-fit: cover;
      border-radius: 12px;
    }
  }
  .home-nav-menu {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 94px;
    gap: 10px;
    .nav-menu-item {
      width: 114px;
      height: 94px;
      border-radius: 8px;
      .name {
        font-size: 14px;
        font-weight: bold;
        margin: 16px 0 8px 12px;
      }
      .get-btn {
        padding: 6px;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.8);
        font-size: 10px;
        margin-left: 12px;
        box-sizing: border-box;
        max-width: 50px;
      }
      &:nth-child(1) {
        .name,
        .get-btn {
          color: #206597;
        }
      }
      &:nth-child(2) {
        .name,
        .get-btn {
          color: #1663f8;
        }
      }
      &:nth-child(3) {
        .name,
        .get-btn {
          color: #dc2626;
        }
      }
    }
  }
  .hot-recommend {
    width: 100%;
    box-sizing: border-box;
    margin: 20px 0;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #1d1f20;
      margin-bottom: 20px;
    }
  }
}
