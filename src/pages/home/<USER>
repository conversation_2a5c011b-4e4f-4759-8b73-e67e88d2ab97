import { memo } from 'react';
import { ScrollView, Swiper, SwiperItem, View, Image, Text } from '@tarojs/components';
import CustomHeader from '@/components/CustomHeader';
import Position from '@/components/Position';
import './index.less';
import { airTour, jaunt, lowAltitudeTourism } from '@/utils/img';
import { toUrl } from '@/utils';
import RecommendList from '@/components/RecommendList';

const HOME_NAV_MENU = [
  { name: '短途旅行', imgUrl: jaunt, path: '/pages/jauntAirLine/index' },
  { name: '空中游览', imgUrl: airTour, path: '/pages/product/index' },
  { name: '低空航线', imgUrl: lowAltitudeTourism, path: '/pages/lowAltitudeAirline/index' },
];
const Index = () => {
  return (
    <>
      <View className={'home-page'}>
        <ScrollView className='scrollview' scrollY scrollWithAnimation enableBackToTop={true}>
          <CustomHeader customElement={<Position />} bgColor={'transparent'} />
          <View className={'swapper-box'}>
            <Swiper
              className='test-h'
              indicatorColor='#fff'
              indicatorActiveColor='#F3564B'
              circular
              indicatorDots
              autoplay
            >
              {HOME_NAV_MENU?.map((item, index) => (
                <SwiperItem key={index}>
                  <Image src={item.imgUrl} className={'swiper-bg'} />
                </SwiperItem>
              ))}
            </Swiper>
          </View>
          <View className={'home-nav-menu'}>
            {HOME_NAV_MENU?.map((item, index) => (
              <View
                className={'nav-menu-item'}
                key={index}
                style={{
                  background: `url(${item.imgUrl}) no-repeat center center/cover`,
                }}
                onClick={() => toUrl(item?.path)}
              >
                <View className={'name'}>{item?.name}</View>
                <View className={'get-btn'}>立即预定</View>
              </View>
            ))}
          </View>
          <View className={'hot-recommend'}>
            <View className={'title'}>
              <Text>热门推荐</Text>
            </View>
            <RecommendList />
          </View>
        </ScrollView>
      </View>
    </>
  );
};
export default memo(Index);
