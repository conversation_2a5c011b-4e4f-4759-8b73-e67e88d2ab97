import { memo, useEffect, useState } from 'react';
import { Button, Image, View, Text } from '@tarojs/components';
import { ArrowTransfer, Plus, Minus } from '@nutui/icons-react-taro';
import './index.less';
import { calendarIcon } from '@/utils/img';
import CustomCalendar from '@/components/CustomCalendar';
import CustomTag from '@/components/CustomTag';
import dayjs from 'dayjs';
import { DATE_FORMAT, SUCCESS_CODE } from '@/utils/constant';
import api from '@/api';
import { Picker, PickerOptions } from '@nutui/nutui-react-taro';

interface FlightSegment {
  departureCity: string;
  arrivalCity: string;
  date: string;
}

interface SearchAirlineProps {
  type?: 'oneWay' | 'roundTrip' | 'multipass';
  getData?: (data: FlightSegment[]) => void;
}

interface OptionProps {
  text?: string;
  value?: string;
  label?: string;
}
const SearchAirline = ({ type = 'oneWay', getData }: SearchAirlineProps) => {
  const [options, setOptions] = useState<OptionProps[]>([]);
  const [depVal, setDepVal] = useState<OptionProps>(); //出发城市
  const [arrVal, setArrVal] = useState<OptionProps>(); //到达城市
  const [dateVal, setDateVal] = useState<string>(dayjs().format(DATE_FORMAT)); //航班日期
  const [returnDateVal, setReturnDateVal] = useState<string>(
    dayjs().add(2, 'day').format(DATE_FORMAT)
  ); //返程日期
  const [dateShow, setDateShow] = useState(''); //日期选择框是否显示
  const [currentSegment, setCurrentSegment] = useState(0); //当前编辑的航段

  // 多程航线数据
  const [multipassSegments, setMultipassSegments] = useState<FlightSegment[]>([
    { departureCity: '', arrivalCity: '', date: dayjs().format(DATE_FORMAT) },
    {
      departureCity: '',
      arrivalCity: '',
      date: dayjs().add(1, 'day').format(DATE_FORMAT),
    },
  ]);
  /**
   * 获取短途航线城市数据
   */
  const getShortCity = async () => {
    const { code, data } = await api.shortRouteV10CityCreate();
    if (code === SUCCESS_CODE && data) {
      let arr: any[] = [];
      data?.titleLabels?.[0]?.shortRouteCityList?.map(item => {
        arr.push({
          text: item.airportName,
          value: item.airportCode,
          label: item.airportName // 保留label字段以兼容现有逻辑
        });
      });
      console.log(arr);
      setOptions(arr);
      setArrVal({
        label: data?.arrAirportName,
        value: data?.arrAirportCode,
        text: data?.arrAirportName
      });
      setDepVal({
        label: data?.depAirportName,
        value: data?.depAirportCode,
        text: data?.depAirportName
      });
    }
  };

  useEffect(() => {
    getShortCity();
  }, []);
  //渲染地址选择器
  const renderAddress = (val: string, setVal: any, segmentIndex?: number, isArrival?: boolean) => {
    const [visible, setVisible] = useState(false);

    // 获取显示的值
    const displayVal =
      segmentIndex !== undefined && type === 'multipass'
        ? isArrival
          ? multipassSegments[segmentIndex].arrivalCity
          : multipassSegments[segmentIndex].departureCity
        : val;

    // 处理选择确认
    const onConfirm = (selectedOptions: any[]) => {
      console.log('Picker onConfirm:', selectedOptions);
      const selectedOption = selectedOptions?.[0];

      if (selectedOption) {
        if (segmentIndex !== undefined && type === 'multipass') {
          setMultipassSegments(prev => {
            const newSegments = [...prev];
            if (isArrival) {
              newSegments[segmentIndex].arrivalCity = selectedOption.text || selectedOption.label;
            } else {
              newSegments[segmentIndex].departureCity = selectedOption.text || selectedOption.label;
            }
            return newSegments;
          });
        } else {
          setVal({
            label: selectedOption.text || selectedOption.label,
            value: selectedOption.value
          });
        }
      }
      setVisible(false);
    };

    // 获取当前选中的值（用于Picker的defaultValue）
    const getCurrentValue = () => {
      if (!displayVal || options.length === 0) return [];

      const foundOption = options.find(option => option.label === displayVal);
      return foundOption && foundOption.value ? [foundOption.value] : [];
    };

    return (
      <>
        <View
          className={`city-name ${displayVal ? 'active' : ''}`}
          onClick={() => setVisible(true)}
        >
          {displayVal || '请选择'}
        </View>
        <Picker
          visible={visible}
          title='选择出发/到达城市'
          options={[options as any]}
          defaultValue={getCurrentValue()}
          onConfirm={onConfirm}
          onCancel={() => setVisible(false)}
        />
      </>
    );
  };
  //交换出发城市和到达城市
  const exchangeCity = (segmentIndex?: number) => {
    if (segmentIndex !== undefined && type === 'multipass') {
      // 交换多程航线的城市
      setMultipassSegments(prev => {
        const newSegments = [...prev];
        const temp = newSegments[segmentIndex].departureCity;
        newSegments[segmentIndex].departureCity = newSegments[segmentIndex].arrivalCity;
        newSegments[segmentIndex].arrivalCity = temp;
        return newSegments;
      });
    } else {
      setDepVal(arrVal);
      setArrVal(depVal);
    }
  };

  // 添加航段
  const addSegment = () => {
    if (multipassSegments.length < 5) {
      setMultipassSegments(prev => [
        ...prev,
        { departureCity: '', arrivalCity: '', date: dayjs().format(DATE_FORMAT) },
      ]);
    }
  };

  // 删除航段
  const removeSegment = (index: number) => {
    if (multipassSegments.length > 2) {
      setMultipassSegments(prev => prev.filter((_, i) => i !== index));
    }
  };

  // 更新日期
  const updateDate = (date: string, segmentIndex?: number) => {
    if (segmentIndex !== undefined && type === 'multipass') {
      setMultipassSegments(prev => {
        const newSegments = [...prev];
        newSegments[segmentIndex].date = date;
        return newSegments;
      });
    } else if (type === 'roundTrip' && segmentIndex === 1) {
      setReturnDateVal(date);
    } else {
      setDateVal(date);
    }
  };

  // 准备提交数据
  const prepareData = () => {
    let segments: FlightSegment[] = [];

    if (type === 'oneWay') {
      segments = [
        {
          departureCity: depVal?.value || '',
          arrivalCity: arrVal?.value || '',
          date: dateVal,
        },
      ];
    } else if (type === 'roundTrip') {
      segments = [
        {
          departureCity: depVal?.value || '',
          arrivalCity: arrVal?.value || '',
          date: dateVal,
        },
        {
          departureCity: arrVal?.value || '',
          arrivalCity: depVal?.value || '',
          date: returnDateVal,
        },
      ];
    } else if (type === 'multipass') {
      segments = multipassSegments.filter(
        segment => segment.departureCity && segment.arrivalCity && segment.date
      );
    }

    return segments;
  };

  // 渲染城市选择框
  const renderCitySelector = (segmentIndex?: number) => {
    // 如果是多程航线，显示序号
    // const showHeader = type === 'multipass' && segmentIndex !== undefined;
    // // 判断是否可以删除航段
    // const canRemove =
    //   type === 'multipass' && multipassSegments.length > 2 && segmentIndex !== undefined;

    return (
      <View className={`search-item`}>
        <View className={'city-select dep-city'}>
          <View className={'desc'}>出发城市</View>
          {type === 'multipass' && segmentIndex !== undefined
            ? renderAddress('', null, segmentIndex, false)
            : renderAddress(depVal?.label, setDepVal)}
        </View>
        <View
          className={'exchange-icon'}
          onClick={() => (segmentIndex !== undefined ? exchangeCity(segmentIndex) : exchangeCity())}
        >
          <ArrowTransfer width={12} height={12} color={'#B5BACA'} />
        </View>
        <View className={'city-select arr-city'}>
          <View className={'desc'}>到达城市</View>
          {type === 'multipass' && segmentIndex !== undefined
            ? renderAddress('', null, segmentIndex, true)
            : renderAddress(arrVal?.label, setArrVal)}
        </View>
      </View>
    );
  };

  // 日期格式已修改为YYYY-MM-DD

  // 渲染日期选择框
  const renderDateSelector = (segmentIndex?: number) => {
    // 获取显示的日期值
    const dateValue =
      segmentIndex !== undefined && type === 'multipass'
        ? multipassSegments[segmentIndex].date
        : dateVal;

    // 点击日期选择框的处理函数
    const handleDateClick = () => {
      if (segmentIndex !== undefined) {
        setCurrentSegment(segmentIndex);
        setDateShow('multipass');
      } else {
        setDateShow('oneWay');
      }
    };

    return (
      <View className={'search-item time-select-box'}>
        <View className={'city-select dep-city'} onClick={handleDateClick}>
          <View className={'desc'}>航班日期</View>
          <View className={'val'}>{dateValue}</View>
        </View>
        <View className={'city-select arr-city'}>
          <Image src={calendarIcon} className={'calendar-icon'} />
        </View>
      </View>
    );
  };

  // 渲染往返航线的日期选择框
  const renderRoundTripDateSelector = () => {
    // 计算日期间隔
    const calculateDateInterval = () => {
      try {
        const depDate = dayjs(dateVal);
        const retDate = dayjs(returnDateVal);
        if (depDate.isValid() && retDate.isValid()) {
          const diffDays = retDate.diff(depDate, 'day');
          return `${Math.abs(diffDays)}天`;
        }
      } catch (e) {}
      return '0天';
    };

    return (
      <View className={'search-item time-select-box'}>
        <View
          className={'city-select dep-city'}
          onClick={() => {
            setCurrentSegment(0);
            setDateShow('roundTrip');
          }}
        >
          <View className={'desc'}>去程</View>
          <View className={'val'}>{dateVal}</View>
        </View>
        <View className={'interval-time'}>{calculateDateInterval()}</View>
        <View
          className={'city-select arr-city'}
          onClick={() => {
            setCurrentSegment(1);
            setDateShow('roundTrip');
          }}
        >
          <View className={'desc'}>返程</View>
          <View className={'val'}>{returnDateVal}</View>
        </View>
      </View>
    );
  };

  return (
    <>
      <View className={'airline-search-box'}>
        {/* 单程和往返航线的城市选择框 */}
        {(type === 'oneWay' || type === 'roundTrip') && renderCitySelector()}
        {/* 单程航线的日期选择框 */}
        {type === 'oneWay' && renderDateSelector()}
        {/* 往返航线的日期选择框 */}
        {type === 'roundTrip' && renderRoundTripDateSelector()}
        {type === 'multipass' && (
          <>
            {multipassSegments.map((_item, index) => (
              <View className={'card-box multipass-segment'} key={index}>
                <View className={'segment-header'}>
                  <Text className={'segment-number'}>航段 {index! + 1}</Text>
                  {multipassSegments.length > 2 && (
                    <Button className={'remove-segment-btn'} onClick={() => removeSegment(index!)}>
                      <Minus width={16} height={16} color={'#B5BACA'} />
                    </Button>
                  )}
                </View>
                {renderCitySelector(index)}
                {renderDateSelector(index)}
              </View>
            ))}
            {multipassSegments.length < 5 && (
              <CustomTag
                bgColor={'#F7F8FA'}
                color={'#124072'}
                onClick={() => addSegment()}
                size={'large'}
              >
                <Plus width={12} height={12} color={'#124072'} />
                <Text className={'add-segment-text'}>新增行程</Text>
              </CustomTag>
            )}
          </>
        )}
        <Button
          className={'search-btn'}
          onClick={() => {
            const segments = prepareData();
            getData?.(segments);
          }}
        >
          查询价格
        </Button>
      </View>
      <CustomCalendar
        customActionSheetProps={{
          visible: !!dateShow,
          onCancel: () => setDateShow(''),
          onConfirm: val => {
            if (!val) return;
            const selectedDate = val as string;
            if (dateShow === 'oneWay') {
              setDateVal(selectedDate);
            } else if (dateShow === 'roundTrip') {
              // 处理往返日期选择
              if (currentSegment === 0) {
                setDateVal(selectedDate);
              } else {
                setReturnDateVal(selectedDate);
              }
            } else if (dateShow === 'multipass') {
              // 更新多程航线的日期
              updateDate(selectedDate, currentSegment);
            }
            setDateShow('');
          },
        }}
      />
    </>
  );
};
export default memo(SearchAirline);
