import { TOKEN_NAME } from '@/utils/constant';
import Taro from '@tarojs/taro';
import { _localStorage, clearUserSession, toast, toUrl } from '@/utils';
import { toLowerCase } from '@tarojs/components/lib/vue2/vue-component-lib/utils';
import dayjs from 'dayjs';

// 全局常量声明
declare const SERVER_URL: string;

interface RequestOptions {
  path: string;
  method?: keyof Taro.request.Method;
  type?: any;
  body?: any;
  query?: any;
  format?: any;
  secure?: boolean;
}

// 常量定义
const TOKEN_REFRESH_ADVANCE_TIME = 5; // 提前5分钟刷新token
const PUBLIC_API_PATHS = ['/city', '/recommend']; // 公开API路径

// 业务错误码映射
const BUSINESS_ERROR_CODES = {
  200: '成功',
  404: '请求的资源不存在',
  10000: '系统异常，请稍后重试',
  10001: '参数校验异常',
  10002: '请求头错误',
  10003: '权限不足',
  10004: '登录超时，请重新登录',
  10005: '数据不存在',
  10006: '请求太过频繁，请稍后再试',
  10007: '您有未支付订单，请先完成支付或取消订单',
  10008: '订单不存在',
  10009: '当前订单状态不支持该操作',
  10010: '订单取消失败',
  10011: '订单退票失败',
  10012: '请求操作失败',
} as const;

// 通用错误消息
const ERROR_MESSAGES = {
  TOKEN_EXPIRED: '登录状态已失效，请重新登录',
  TOKEN_INVALID: '认证失败，请重新登录',
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  SERVER_ERROR: '服务器繁忙，请稍后重试',
  REQUEST_FAILED: '请求失败，请稍后重试',
} as const;

// 工具函数：显示toast并抛出错误
const showToastAndThrow = (message: string, errorMessage?: string): never => {
  toast.info(message);
  throw new Error(errorMessage || message);
};

// 根据业务错误码获取错误消息
const getBusinessErrorMessage = (code: number): string => {
  return (
    BUSINESS_ERROR_CODES[code as keyof typeof BUSINESS_ERROR_CODES] || ERROR_MESSAGES.REQUEST_FAILED
  );
};

// 判断是否为需要重新登录的错误码
const isAuthError = (code: number): boolean => {
  return code === 401 || code === 10003 || code === 10004; // 401未授权、10003权限不足、10004登录超时
};

// Token管理
const getToken = (): Record<string, string> => {
  const token = _localStorage.getItem(TOKEN_NAME);
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// 通用token过期检查函数
const checkTokenExpiration = (tokenKey: string, advanceMinutes = 0): boolean => {
  const tokenTimeOut = _localStorage.getItem(tokenKey);
  if (!tokenTimeOut) return true;

  try {
    const expireTime = dayjs(tokenTimeOut);
    if (!expireTime.isValid()) {
      console.error(`Invalid ${tokenKey} format:`, tokenTimeOut);
      return true;
    }

    const currentTime = dayjs();
    const checkTime =
      advanceMinutes > 0 ? expireTime.subtract(advanceMinutes, 'minute') : expireTime;

    return currentTime.isAfter(checkTime);
  } catch (error) {
    console.error(`Error parsing ${tokenKey}:`, error);
    return true;
  }
};

// 检查access token是否过期（提前5分钟）
const isTokenExpired = (): boolean => {
  return checkTokenExpiration('accessTokenTimeOut', TOKEN_REFRESH_ADVANCE_TIME);
};

// 检查refresh token是否过期
const isRefreshTokenExpired = (): boolean => {
  return checkTokenExpiration('refreshTokenTimeOut');
};

// 刷新token的并发控制
let isRefreshing = false;
let failedQueue: Array<{ resolve: Function; reject: Function }> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });
  failedQueue = [];
};

// 刷新token
const refreshToken = async (): Promise<boolean> => {
  if (isRefreshing) {
    // 如果正在刷新，将请求加入队列
    return new Promise((resolve, reject) => {
      failedQueue.push({ resolve, reject });
    });
  }

  isRefreshing = true;

  try {
    const refreshTokenValue = _localStorage.getItem('refreshToken');
    const uid = _localStorage.getItem('uid');

    // 验证必要参数
    if (!refreshTokenValue || !uid) {
      showToastAndThrow(ERROR_MESSAGES.TOKEN_EXPIRED, '缺少必要的认证信息');
    }

    if (isRefreshTokenExpired()) {
      showToastAndThrow(ERROR_MESSAGES.TOKEN_EXPIRED, '刷新令牌已过期');
    }

    const response = await Taro.request({
      url: `${SERVER_URL}/common/v1.0/refresh`,
      method: 'POST',
      data: { refreshToken: refreshTokenValue, uid },
      header: {
        'content-type': 'application/json',
        appSign: 'HXGW',
      },
    });

    // 检查响应状态
    if (response.statusCode !== 200) {
      showToastAndThrow(ERROR_MESSAGES.TOKEN_EXPIRED, `刷新请求失败: ${response.statusCode}`);
    }

    if (!response.data?.success || !response.data?.data) {
      const errorMsg = response.data?.message || '刷新响应数据无效';
      showToastAndThrow(ERROR_MESSAGES.TOKEN_EXPIRED, errorMsg);
    }

    const {
      accessToken,
      refreshToken: newRefreshToken,
      accessTokenTimeOut,
      refreshTokenTimeOut,
    } = response.data.data;

    // 验证返回的token数据
    if (!accessToken || !newRefreshToken || !accessTokenTimeOut || !refreshTokenTimeOut) {
      showToastAndThrow(ERROR_MESSAGES.TOKEN_EXPIRED, '返回的token数据不完整');
    }

    // 更新存储的token信息
    _localStorage.setItem(TOKEN_NAME, accessToken);
    _localStorage.setItem('refreshToken', newRefreshToken);
    _localStorage.setItem('accessTokenTimeOut', accessTokenTimeOut);
    _localStorage.setItem('refreshTokenTimeOut', refreshTokenTimeOut);

    processQueue(null, accessToken);
    return true;
  } catch (error) {
    console.error('Token刷新失败:', error);
    const errorMessage = error instanceof Error ? error.message : ERROR_MESSAGES.TOKEN_EXPIRED;
    toast.info(
      errorMessage.includes('过期') || errorMessage.includes('无效')
        ? ERROR_MESSAGES.TOKEN_EXPIRED
        : errorMessage
    );

    processQueue(error, null);
    handleTokenExpired();
    return false;
  } finally {
    isRefreshing = false;
  }
};

// 处理token过期的统一逻辑
const handleTokenExpired = (): void => {
  clearUserSession();
  toUrl('/pages/login/index');
};

// 检查是否为公开API
const isPublicApi = (path: string): boolean => {
  return PUBLIC_API_PATHS.some(publicPath => path.includes(publicPath));
};

// 自动刷新token（仅在token即将过期时）
const autoRefreshToken = async (): Promise<void> => {
  // 只有在token即将过期时才自动刷新
  if (isTokenExpired()) {
    if (isRefreshing) {
      // 如果正在刷新token，将请求加入队列
      return new Promise((resolve, reject) => {
        failedQueue.push({ resolve, reject });
      });
    }

    try {
      await refreshToken();
    } catch (error) {
      // 自动刷新失败，不阻塞请求，让后端来判断
      console.warn('自动刷新token失败:', error);
    }
  }
};

// 主请求函数
const request = async <T = any, _E = any>(
  options: RequestOptions & {
    isloading?: boolean;
  }
): Promise<T> => {
  const { path, method = 'GET', body, query, type, isloading = false, ...params } = options;

  // 验证必要参数
  if (!path) {
    showToastAndThrow('请求路径不能为空');
  }

  // 自动刷新即将过期的token（不阻塞请求）
  const token = _localStorage.getItem(TOKEN_NAME);
  if (token && !isPublicApi(path)) {
    try {
      await autoRefreshToken();
    } catch (error) {
      // 自动刷新失败不影响请求继续进行
      console.warn('自动刷新token失败，继续发送请求:', error);
    }
  }

  let loadingShown = false;

  try {
    // 显示加载状态
    if (isloading) {
      Taro.showLoading({ title: '加载中...' });
      loadingShown = true;
    }

    // 构建请求头（始终携带token，让后端判断权限）
    const headers = {
      'content-type': type || 'application/json',
      appSign: 'HXGW',
      uid: _localStorage.getItem('uid') || '',
      ...getToken(), // 有token就携带，没有也不阻塞请求
      ...((params as any)?.header ?? {}),
    };

    // 发起请求
    const response = await Taro.request({
      url: `${SERVER_URL}${path}`,
      method,
      data: body || query,
      header: headers,
      timeout: 30000, // 30秒超时
      ...params,
    }).catch(error => {
      // 处理网络请求异常
      if (error?.errMsg) {
        if (error.errMsg.includes('timeout')) {
          showToastAndThrow(ERROR_MESSAGES.NETWORK_ERROR, '请求超时');
        } else if (error.errMsg.includes('网络') || error.errMsg.includes('network')) {
          showToastAndThrow(ERROR_MESSAGES.NETWORK_ERROR, '网络连接失败');
        }
      }
      throw error;
    });

    // 隐藏加载状态
    if (loadingShown) {
      Taro.hideLoading();
      loadingShown = false;
    }

    // 处理响应
    return await handleResponse(response, options);
  } catch (error) {
    // 确保隐藏加载状态
    if (loadingShown) {
      Taro.hideLoading();
    }

    handleError(error);
    throw error;
  }
};

// 处理响应的统一逻辑
const handleResponse = async <T>(
  response: Taro.request.SuccessCallbackResult,
  originalOptions: any
): Promise<T> => {
  // 检查HTTP状态码
  if (response.statusCode < 200 || response.statusCode >= 300) {
    const errorMessage = getBusinessErrorMessage(response.statusCode);
    throw { statusCode: response.statusCode, message: errorMessage };
  }

  const { data } = response;
  const businessCode = data?.code;

  // 处理业务成功响应
  if (businessCode === 200 || data?.success === true) {
    return data;
  }

  // 处理业务错误码
  if (businessCode && businessCode !== 200) {
    const errorMessage = data?.message || getBusinessErrorMessage(businessCode);

    // 处理认证相关错误（10003权限不足、10004登录超时）
    if (isAuthError(businessCode)) {
      // 尝试刷新token（仅对10004登录超时）
      if (businessCode === 10004 && !isRefreshing && !isPublicApi(originalOptions.path)) {
        try {
          const refreshSuccess = await refreshToken();
          if (refreshSuccess) {
            return request(originalOptions);
          }
        } catch (refreshError) {
          console.error('刷新token失败:', refreshError);
        }
      }

      // 认证失败，清理会话并跳转登录
      handleTokenExpired();
      showToastAndThrow(errorMessage);
    }

    // 处理其他业务错误
    showToastAndThrow(errorMessage);
  }

  // 兜底处理：如果没有明确的错误码但success为false
  if (data?.success === false) {
    const errorMessage = data?.message || ERROR_MESSAGES.REQUEST_FAILED;
    showToastAndThrow(errorMessage);
  }

  return data;
};

// 简化的错误处理函数
const handleError = (error: any): void => {
  console.error('请求错误:', error);

  // 如果错误已经在handleResponse中处理过toast，这里就不再重复处理
  if (error?.handled) {
    return;
  }

  let errorMessage: string = ERROR_MESSAGES.REQUEST_FAILED;
  let shouldShowToast = true;

  // 处理网络错误
  if (error?.errMsg) {
    if (error.errMsg.includes('timeout')) {
      errorMessage = '请求超时，请稍后重试';
    } else if (error.errMsg.includes('网络') || error.errMsg.includes('network')) {
      errorMessage = ERROR_MESSAGES.NETWORK_ERROR;
    } else if (error.errMsg.includes('abort')) {
      shouldShowToast = false; // 请求被取消，不显示错误
    }
  }

  // 处理HTTP状态码错误
  if (error?.statusCode) {
    errorMessage = getBusinessErrorMessage(error.statusCode);

    // 认证错误已在handleResponse中处理
    if (isAuthError(error.statusCode)) {
      shouldShowToast = false;
    }
  }

  // 处理已知的业务错误消息
  if (error?.message && typeof error.message === 'string' && error.message.length < 100) {
    errorMessage = error.message;
  }

  // 显示错误提示
  if (shouldShowToast) {
    Taro.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 2000,
    });
  }
};

export default request;
export {
  refreshToken,
  isTokenExpired,
  isRefreshTokenExpired,
  getBusinessErrorMessage,
  BUSINESS_ERROR_CODES,
  isAuthError
};
