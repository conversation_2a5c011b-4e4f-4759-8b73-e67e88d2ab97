{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "low-altitude-flight-app", "setting": {"compileHotReLoad": false, "urlCheck": false, "bigPackageSizeSupport": true}, "condition": {"miniprogram": {"list": [{"name": "首页", "pathName": "pages/home/<USER>", "query": "", "scene": null}, {"name": "登录", "pathName": "pages/login/index", "query": "", "scene": null}, {"name": "验证码登录", "pathName": "pages/verCodeLogin/index", "query": "", "scene": null}, {"name": "我的", "pathName": "pages/userCenter/index", "query": "", "scene": null}, {"name": "用户信息", "pathName": "pages/userCenter/userInfo/index", "query": "", "scene": null}, {"name": "短途航线", "pathName": "pages/jauntAirLine/index", "query": "", "scene": null}, {"name": "低空航线", "pathName": "pages/lowAltitudeAirline/index", "query": "", "scene": null}, {"name": "低空航线-详情", "pathName": "pages/lowAltitudeAirline/detail/index", "query": "", "scene": null}, {"name": "产品", "pathName": "pages/product/index", "query": "", "scene": null}, {"name": "空中游览详情", "pathName": "pages/product/airTourDetail/index", "query": "", "scene": null}, {"name": "空中游览订单详情", "pathName": "pages/product/airTourOrderDetail/index", "query": "", "scene": null}, {"name": "订单", "pathName": "pages/orders/index", "query": "", "scene": null}, {"name": "旅游订单 - 订单详情", "pathName": "pages/orders/travelOrders/orderDetails/index", "query": "", "scene": null}, {"name": "旅游订单 - 退款详情", "pathName": "pages/orders/travelOrders/refundDetails/index", "query": "", "scene": null}, {"name": "旅游订单 - 改期", "pathName": "pages/orders/travelOrders/aerialTour/index", "query": "", "scene": null}, {"name": "低空航线订单 - 详情", "pathName": "pages/orders/lowAirLineOrder/detail/index", "query": "", "scene": null}, {"name": "低空航线订单 - 改期", "pathName": "pages/orders/lowAirLineOrder/changeDate/index", "query": "", "scene": null}, {"name": "发票", "pathName": "pages/invoice/index", "query": "", "scene": null}, {"name": "短途航线-航班列表", "pathName": "pages/jauntAirLine/flightList/index", "query": "", "scene": null}, {"name": "短途航线-选择舱位", "pathName": "pages/jauntAirLine/selectCabin/index", "query": "", "scene": null}, {"name": "短途航线-旅客信息", "pathName": "pages/jauntAirLine/passengerInfo/index", "query": "", "scene": null}, {"name": "短途航线-支付", "pathName": "pages/jauntAirLine/payment/index", "query": "", "scene": null}]}}, "libVersion": "3.8.7"}